{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/About.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/About.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/About.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,sVAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmV,GAChX,iHACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/About.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/About.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/About.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,sVAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,sVAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6V,GAC1X,2HACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,sVAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyU,GACtW,uGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/Intro.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Intro.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Intro.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,sVAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAmV,GAChX,iHACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/Intro.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Intro.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Intro.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,sVAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+T,GAC5V,6FACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/Testimonials.tsx"], "sourcesContent": ["import React from 'react'\r\nimport Image from 'next/image'\r\n\r\nconst Testimonials = () => {\r\n  const testimonials = [\r\n    {\r\n      name: \"\",\r\n      role: \"\",\r\n      image: \"/person-1.png\",\r\n      quote: \"\"\r\n    },\r\n    {\r\n      name: \"\",\r\n      role: \"\",\r\n      image: \"/person-2.png\",\r\n      quote: \"\"\r\n    },\r\n    {\r\n      name: \"\",\r\n      role: \"\",\r\n      image: \"/person-3.png\",\r\n      quote: \"\"\r\n    }\r\n  ]\r\n\r\n  return (\r\n    <section id=\"testimonials\" className=\"bg-gray-10 py-20\">\r\n      <div className=\"max-container padding-container\">\r\n        <div className=\"text-left mb-16\">\r\n          <h2 className=\"bold-40 lg:bold-52 text-gray-90 mb-6\">\r\n            Testimonials\r\n          </h2>\r\n          <p className=\"regular-16 text-gray-50 max-w-2xl\">\r\n            Real stories and experiences from our community members and healthcare professionals.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n          {testimonials.map((testimonial, index) => (\r\n            <div key={index} className=\"bg-white rounded-2xl p-8 relative shadow-lg hover:shadow-xl transition-all border border-primary-100 min-h-[300px]\">\r\n              <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mb-6\">\r\n                <Image\r\n                  src=\"/quote.svg\"\r\n                  alt=\"Quote\"\r\n                  width={20}\r\n                  height={20}\r\n                  className=\"text-primary-600\"\r\n                />\r\n              </div>\r\n              <div className=\"mb-6 h-24 flex items-center justify-center\">\r\n                <p className=\"regular-16 text-gray-300 italic text-center\">\r\n                  Testimonial content will be added here\r\n                </p>\r\n              </div>\r\n              <div className=\"flex items-center gap-4 absolute bottom-8 left-8 right-8\">\r\n                <div className=\"relative\">\r\n                  <div className=\"w-12 h-12 bg-gray-200 rounded-full\"></div>\r\n                  <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-primary-400 rounded-full border-2 border-white\"></div>\r\n                </div>\r\n                <div>\r\n                  <div className=\"h-4 bg-gray-200 rounded w-24 mb-2\"></div>\r\n                  <div className=\"h-3 bg-gray-100 rounded w-32\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Testimonials\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,4TAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAGrD,4TAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAKnD,4TAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,4TAAC;4BAAgB,WAAU;;8CACzB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,sNAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAE,WAAU;kDAA8C;;;;;;;;;;;8CAI7D,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;8DACb,4TAAC;oDAAI,WAAU;;;;;;8DACf,4TAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,4TAAC;;8DACC,4TAAC;oDAAI,WAAU;;;;;;8DACf,4TAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;2BAtBX;;;;;;;;;;;;;;;;;;;;;AA+BtB;uCAEe", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/app/page.tsx"], "sourcesContent": ["import About from \"@/components/About\";\r\nimport GraphsandCharts from \"@/components/GraphsandCharts\";\r\nimport Intro from \"@/components/Intro\";\r\nimport Testimonials from \"@/components/Testimonials\";\r\n\r\nexport default function Home() {\r\n  return(\r\n    <div className=\"space-y-20\">\r\n      <Intro/>\r\n      <About/>\r\n      <GraphsandCharts/>\r\n      <Testimonials/>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,6MAAK;;;;;0BACN,4TAAC,6MAAK;;;;;0BACN,4TAAC,uNAAe;;;;;0BAChB,4TAAC,oNAAY;;;;;;;;;;;AAGnB", "debugId": null}}]}