# Data manipulation and analysis
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_auc_score, roc_curve
from sklearn.feature_selection import SelectKBest, f_classif

# Deep Learning libraries
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import Dense, Dropout, LSTM, Conv1D, MaxPooling1D, Flatten
    from tensorflow.keras.optimizers import <PERSON>
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
    print("TensorFlow version:", tf.__version__)
except ImportError:
    print("TensorFlow not installed. Will use only traditional ML models.")

# Set random seeds for reproducibility
np.random.seed(42)
try:
    tf.random.set_seed(42)
except:
    pass

# Display settings
plt.style.use('seaborn-v0_8')
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

print("✅ Libraries imported successfully!")
print("📊 Ready for Waterborne Disease Prediction modeling")

# Load the datasets
print("📁 Loading Northeast India datasets...")

# Load disease outbreak data
disease_df = pd.read_csv('northeast_states_disease_outbreaks.csv')
print(f"Disease outbreak data: {disease_df.shape}")

# Load water quality data
water_df = pd.read_csv('northeast_water_quality_data.csv')
print(f"Water quality data: {water_df.shape}")

print("\n🔍 Disease Outbreak Data Overview:")
print(disease_df.head())
print("\nColumns:", disease_df.columns.tolist())
print("\nData types:")
print(disease_df.dtypes)

print("\n🔍 Water Quality Data Overview:")
print(water_df.head())
print("\nColumns:", water_df.columns.tolist())
print("\nData types:")
print(water_df.dtypes)

# Basic data exploration
print("\n📊 Disease Data Analysis:")
print(f"Total outbreaks: {len(disease_df)}")
print(f"States covered: {disease_df['Northeast_State'].nunique()}")
print(f"Disease types: {disease_df['Disease_Illness'].value_counts()}")
print(f"\nOutbreaks by state:")
print(disease_df['Northeast_State'].value_counts())

print("\n🌊 Water Quality Data Analysis:")
print(f"Total water samples: {len(water_df)}")
print(f"States with water data: {water_df['northeast_state'].nunique()}")
print(f"Water quality by state:")
print(water_df['northeast_state'].value_counts())

# Check for missing values
print("\n❓ Missing Values:")
print("Disease data missing values:")
print(disease_df.isnull().sum())
print("\nWater quality missing values:")
print(water_df.isnull().sum())

# Data Preprocessing and Feature Engineering
print("🔧 Starting Data Preprocessing...")

# 1. Clean and prepare disease data
print("\n1️⃣ Processing Disease Data:")

# Convert date columns
disease_df['Date_of_Start_of_Outbreak'] = pd.to_datetime(disease_df['Date_of_Start_of_Outbreak'], format='%d-%m-%y', errors='coerce')
disease_df['Date_of_Reporting'] = pd.to_datetime(disease_df['Date_of_Reporting'], format='%d-%m-%y', errors='coerce')

# Extract year and month for temporal analysis
disease_df['outbreak_year'] = disease_df['Date_of_Start_of_Outbreak'].dt.year
disease_df['outbreak_month'] = disease_df['Date_of_Start_of_Outbreak'].dt.month

# Create severity indicators
disease_df['cases_per_death'] = disease_df['No_of_Cases'] / (disease_df['No_of_Deaths'] + 1)  # +1 to avoid division by zero
disease_df['mortality_rate'] = disease_df['No_of_Deaths'] / disease_df['No_of_Cases']
disease_df['severity_score'] = disease_df['No_of_Cases'] + (disease_df['No_of_Deaths'] * 10)  # Weight deaths more heavily

# Categorize disease types
waterborne_diseases = ['Acute Diarrheal Disease', 'Cholera', 'Hepatitis A']
disease_df['is_waterborne'] = disease_df['Disease_Illness'].isin(waterborne_diseases).astype(int)

# Create outbreak intensity categories
disease_df['outbreak_intensity'] = pd.cut(disease_df['No_of_Cases'], 
                                        bins=[0, 10, 25, 50, float('inf')], 
                                        labels=['Low', 'Medium', 'High', 'Very High'])

print(f"Waterborne disease outbreaks: {disease_df['is_waterborne'].sum()}")
print(f"Total outbreaks: {len(disease_df)}")
print(f"Outbreak intensity distribution:")
print(disease_df['outbreak_intensity'].value_counts())

# 2. Clean and prepare water quality data
print("\n2️⃣ Processing Water Quality Data:")

# Handle missing values in water quality data
water_df['fecal_streptococci'] = water_df['fecal_streptococci'].fillna(water_df['fecal_streptococci'].median())

# Create water quality risk indicators
# WHO/EPA standards for drinking water
water_df['ph_risk'] = ((water_df['ph'] < 6.5) | (water_df['ph'] > 8.5)).astype(int)
water_df['do_risk'] = (water_df['dissolved_oxygen'] < 5).astype(int)  # Low DO indicates pollution
water_df['bod_risk'] = (water_df['bod'] > 5).astype(int)  # High BOD indicates organic pollution
water_df['nitrate_risk'] = (water_df['nitrate_n'] > 10).astype(int)  # WHO guideline
water_df['fecal_coliform_risk'] = (water_df['fecal_coliform'] > 0).astype(int)  # Any presence is risky
water_df['total_coliform_risk'] = (water_df['total_coliform'] > 0).astype(int)

# Create composite water quality index
risk_columns = ['ph_risk', 'do_risk', 'bod_risk', 'nitrate_risk', 'fecal_coliform_risk', 'total_coliform_risk']
water_df['water_quality_risk_score'] = water_df[risk_columns].sum(axis=1)
water_df['water_quality_category'] = pd.cut(water_df['water_quality_risk_score'], 
                                          bins=[-1, 1, 3, 5, 6], 
                                          labels=['Good', 'Fair', 'Poor', 'Very Poor'])

print(f"Water quality categories:")
print(water_df['water_quality_category'].value_counts())
print(f"\nWater quality risk scores:")
print(water_df['water_quality_risk_score'].describe())

# 3. Create training dataset by combining disease and water quality data
print("\n3️⃣ Creating Training Dataset:")

# Aggregate disease data by state and year to match with water quality data
disease_summary = disease_df.groupby(['Northeast_State', 'outbreak_year']).agg({
    'is_waterborne': 'sum',
    'No_of_Cases': 'sum',
    'No_of_Deaths': 'sum',
    'severity_score': 'sum',
    'outbreak_intensity': lambda x: (x == 'High').sum() + (x == 'Very High').sum()  # Count high-intensity outbreaks
}).reset_index()

disease_summary.columns = ['state', 'year', 'waterborne_outbreaks', 'total_cases', 'total_deaths', 'total_severity', 'high_intensity_outbreaks']

# Create binary target: 1 if there were waterborne disease outbreaks, 0 otherwise
disease_summary['has_waterborne_outbreak'] = (disease_summary['waterborne_outbreaks'] > 0).astype(int)

# Create risk level target (multi-class)
disease_summary['risk_level'] = pd.cut(disease_summary['waterborne_outbreaks'], 
                                     bins=[-1, 0, 2, 5, float('inf')], 
                                     labels=[0, 1, 2, 3])  # 0=No Risk, 1=Low, 2=Medium, 3=High

print(f"Disease summary shape: {disease_summary.shape}")
print(f"\nTarget distribution (binary):")
print(disease_summary['has_waterborne_outbreak'].value_counts())
print(f"\nTarget distribution (multi-class):")
print(disease_summary['risk_level'].value_counts())

# Prepare water quality data for merging
water_summary = water_df.groupby(['northeast_state', 'sample_date']).agg({
    'temperature': 'mean',
    'ph': 'mean',
    'dissolved_oxygen': 'mean',
    'bod': 'mean',
    'nitrate_n': 'mean',
    'fecal_coliform': 'mean',
    'total_coliform': 'mean',
    'fecal_streptococci': 'mean',
    'water_quality_risk_score': 'mean'
}).reset_index()

water_summary.columns = ['state', 'year', 'temperature', 'ph', 'dissolved_oxygen', 'bod', 
                        'nitrate_n', 'fecal_coliform', 'total_coliform', 'fecal_streptococci', 'water_quality_risk_score']

print(f"\nWater summary shape: {water_summary.shape}")

# Merge datasets
# Since we have limited water quality data, we'll use a strategy to create more training samples
# We'll assume water quality conditions are relatively stable within a state over nearby years

merged_data = []

for _, disease_row in disease_summary.iterrows():
    state = disease_row['state']
    year = disease_row['year']
    
    # Find water quality data for the same state and closest year
    state_water = water_summary[water_summary['state'] == state]
    
    if len(state_water) > 0:
        # Find closest year
        closest_water = state_water.iloc[(state_water['year'] - year).abs().argsort()[:1]]
        
        if len(closest_water) > 0:
            combined_row = {**disease_row.to_dict(), **closest_water.iloc[0].to_dict()}
            # Remove duplicate columns
            combined_row['state'] = state
            combined_row['year'] = year
            merged_data.append(combined_row)

# Create final training dataset
training_df = pd.DataFrame(merged_data)
print(f"\n✅ Final training dataset shape: {training_df.shape}")
print(f"States in training data: {training_df['state'].unique()}")
print(f"Years covered: {sorted(training_df['year'].unique())}")

# Exploratory Data Analysis
print("📊 Starting Exploratory Data Analysis...")

# Set up plotting
plt.figure(figsize=(20, 15))

# 1. Target variable distribution
plt.subplot(3, 4, 1)
training_df['has_waterborne_outbreak'].value_counts().plot(kind='bar', color=['lightblue', 'coral'])
plt.title('Distribution of Waterborne Outbreaks')
plt.xlabel('Has Outbreak (0=No, 1=Yes)')
plt.ylabel('Count')
plt.xticks(rotation=0)

# 2. Risk level distribution
plt.subplot(3, 4, 2)
training_df['risk_level'].value_counts().sort_index().plot(kind='bar', color='skyblue')
plt.title('Risk Level Distribution')
plt.xlabel('Risk Level (0=None, 1=Low, 2=Med, 3=High)')
plt.ylabel('Count')
plt.xticks(rotation=0)

# 3. Water quality parameters distribution
water_params = ['ph', 'dissolved_oxygen', 'bod', 'nitrate_n', 'fecal_coliform', 'total_coliform']

for i, param in enumerate(water_params):
    plt.subplot(3, 4, i+3)
    training_df[param].hist(bins=15, alpha=0.7, color='lightgreen')
    plt.title(f'{param.replace("_", " ").title()} Distribution')
    plt.xlabel(param.replace('_', ' ').title())
    plt.ylabel('Frequency')

# 9. Water quality risk score vs outbreaks
plt.subplot(3, 4, 9)
outbreak_yes = training_df[training_df['has_waterborne_outbreak'] == 1]['water_quality_risk_score']
outbreak_no = training_df[training_df['has_waterborne_outbreak'] == 0]['water_quality_risk_score']
plt.hist([outbreak_no, outbreak_yes], bins=10, alpha=0.7, label=['No Outbreak', 'Outbreak'], color=['lightblue', 'coral'])
plt.title('Water Quality Risk Score vs Outbreaks')
plt.xlabel('Water Quality Risk Score')
plt.ylabel('Frequency')
plt.legend()

# 10. Outbreaks by state
plt.subplot(3, 4, 10)
state_outbreaks = training_df.groupby('state')['has_waterborne_outbreak'].sum().sort_values(ascending=False)
state_outbreaks.plot(kind='bar', color='orange')
plt.title('Waterborne Outbreaks by State')
plt.xlabel('State')
plt.ylabel('Number of Outbreaks')
plt.xticks(rotation=45)

# 11. Correlation heatmap
plt.subplot(3, 4, 11)
correlation_cols = ['has_waterborne_outbreak', 'water_quality_risk_score', 'ph', 'dissolved_oxygen', 
                   'bod', 'nitrate_n', 'fecal_coliform', 'total_coliform']
corr_matrix = training_df[correlation_cols].corr()
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, fmt='.2f', square=True)
plt.title('Feature Correlation Matrix')

# 12. Temporal analysis
plt.subplot(3, 4, 12)
yearly_outbreaks = training_df.groupby('year')['has_waterborne_outbreak'].sum()
yearly_outbreaks.plot(kind='line', marker='o', color='red')
plt.title('Waterborne Outbreaks Over Time')
plt.xlabel('Year')
plt.ylabel('Number of Outbreaks')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Statistical summary
print("\n📈 Statistical Summary:")
print(f"Total samples in training data: {len(training_df)}")
print(f"Samples with waterborne outbreaks: {training_df['has_waterborne_outbreak'].sum()}")
print(f"Outbreak rate: {training_df['has_waterborne_outbreak'].mean():.2%}")

print("\n🌊 Water Quality Statistics:")
print(training_df[water_params + ['water_quality_risk_score']].describe())

print("\n🔗 Key Correlations with Outbreaks:")
correlations = training_df[correlation_cols].corr()['has_waterborne_outbreak'].sort_values(key=abs, ascending=False)
for feature, corr in correlations.items():
    if feature != 'has_waterborne_outbreak':
        print(f"{feature}: {corr:.3f}")

# Traditional Machine Learning Models
print("🤖 Building Traditional ML Models...")

# Prepare features and target
feature_columns = ['temperature', 'ph', 'dissolved_oxygen', 'bod', 'nitrate_n', 
                  'fecal_coliform', 'total_coliform', 'fecal_streptococci', 'water_quality_risk_score']

# Handle any remaining missing values
X = training_df[feature_columns].fillna(training_df[feature_columns].median())
y_binary = training_df['has_waterborne_outbreak']
y_multiclass = training_df['risk_level'].astype(int)

print(f"Feature matrix shape: {X.shape}")
print(f"Binary target distribution: {y_binary.value_counts().to_dict()}")
print(f"Multi-class target distribution: {y_multiclass.value_counts().to_dict()}")

# Split the data
X_train, X_test, y_train_bin, y_test_bin = train_test_split(X, y_binary, test_size=0.3, random_state=42, stratify=y_binary)
_, _, y_train_multi, y_test_multi = train_test_split(X, y_multiclass, test_size=0.3, random_state=42, stratify=y_multiclass)

# Scale the features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print(f"\nTraining set size: {X_train.shape[0]}")
print(f"Test set size: {X_test.shape[0]}")

# Define models for binary classification
models_binary = {
    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
    'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
    'Logistic Regression': LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000),
    'SVM': SVC(random_state=42, class_weight='balanced', probability=True),
    'Naive Bayes': GaussianNB(),
    'Decision Tree': DecisionTreeClassifier(random_state=42, class_weight='balanced')
}

# Train and evaluate binary classification models
print("\n🎯 Binary Classification Results (Outbreak vs No Outbreak):")
print("=" * 80)

binary_results = {}

for name, model in models_binary.items():
    # Use scaled features for SVM and Logistic Regression, original for tree-based models
    if name in ['SVM', 'Logistic Regression', 'Naive Bayes']:
        model.fit(X_train_scaled, y_train_bin)
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
    else:
        model.fit(X_train, y_train_bin)
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
    
    # Calculate metrics
    accuracy = accuracy_score(y_test_bin, y_pred)
    try:
        auc_score = roc_auc_score(y_test_bin, y_pred_proba)
    except:
        auc_score = 0.5
    
    # Cross-validation score
    if name in ['SVM', 'Logistic Regression', 'Naive Bayes']:
        cv_scores = cross_val_score(model, X_train_scaled, y_train_bin, cv=5, scoring='accuracy')
    else:
        cv_scores = cross_val_score(model, X_train, y_train_bin, cv=5, scoring='accuracy')
    
    binary_results[name] = {
        'accuracy': accuracy,
        'auc': auc_score,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'model': model
    }
    
    print(f"{name:20} | Accuracy: {accuracy:.3f} | AUC: {auc_score:.3f} | CV: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

# Find best binary model
best_binary_model = max(binary_results.items(), key=lambda x: x[1]['cv_mean'])
print(f"\n🏆 Best Binary Model: {best_binary_model[0]} (CV Score: {best_binary_model[1]['cv_mean']:.3f})")

# Multi-class Classification (Risk Levels)
print("\n🎯 Multi-class Classification Results (Risk Levels):")
print("=" * 80)

multiclass_results = {}

for name, model in models_binary.items():
    # Clone the model for multi-class
    if name == 'Random Forest':
        mc_model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
    elif name == 'Gradient Boosting':
        mc_model = GradientBoostingClassifier(n_estimators=100, random_state=42)
    elif name == 'Logistic Regression':
        mc_model = LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000, multi_class='ovr')
    elif name == 'SVM':
        mc_model = SVC(random_state=42, class_weight='balanced', probability=True)
    elif name == 'Naive Bayes':
        mc_model = GaussianNB()
    elif name == 'Decision Tree':
        mc_model = DecisionTreeClassifier(random_state=42, class_weight='balanced')
    
    # Train and evaluate
    if name in ['SVM', 'Logistic Regression', 'Naive Bayes']:
        mc_model.fit(X_train_scaled, y_train_multi)
        y_pred_multi = mc_model.predict(X_test_scaled)
        cv_scores_multi = cross_val_score(mc_model, X_train_scaled, y_train_multi, cv=5, scoring='accuracy')
    else:
        mc_model.fit(X_train, y_train_multi)
        y_pred_multi = mc_model.predict(X_test)
        cv_scores_multi = cross_val_score(mc_model, X_train, y_train_multi, cv=5, scoring='accuracy')
    
    accuracy_multi = accuracy_score(y_test_multi, y_pred_multi)
    
    multiclass_results[name] = {
        'accuracy': accuracy_multi,
        'cv_mean': cv_scores_multi.mean(),
        'cv_std': cv_scores_multi.std(),
        'model': mc_model
    }
    
    print(f"{name:20} | Accuracy: {accuracy_multi:.3f} | CV: {cv_scores_multi.mean():.3f} ± {cv_scores_multi.std():.3f}")

# Find best multi-class model
best_multiclass_model = max(multiclass_results.items(), key=lambda x: x[1]['cv_mean'])
print(f"\n🏆 Best Multi-class Model: {best_multiclass_model[0]} (CV Score: {best_multiclass_model[1]['cv_mean']:.3f})")

# Feature Importance Analysis
print("\n🔍 Feature Importance Analysis:")
print("=" * 50)

# Get feature importance from the best tree-based model
tree_models = ['Random Forest', 'Gradient Boosting', 'Decision Tree']
best_tree_model = None
best_tree_score = 0

for model_name in tree_models:
    if model_name in binary_results and binary_results[model_name]['cv_mean'] > best_tree_score:
        best_tree_score = binary_results[model_name]['cv_mean']
        best_tree_model = binary_results[model_name]['model']
        best_tree_name = model_name

if best_tree_model is not None:
    feature_importance = pd.DataFrame({
        'feature': feature_columns,
        'importance': best_tree_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"Feature Importance from {best_tree_name}:")
    for _, row in feature_importance.iterrows():
        print(f"{row['feature']:25} | {row['importance']:.4f}")
    
    # Plot feature importance
    plt.figure(figsize=(10, 6))
    plt.barh(feature_importance['feature'], feature_importance['importance'])
    plt.title(f'Feature Importance - {best_tree_name}')
    plt.xlabel('Importance')
    plt.tight_layout()
    plt.show()

# Detailed classification report for best binary model
print("\n📊 Detailed Classification Report (Best Binary Model):")
print("=" * 60)

best_model = best_binary_model[1]['model']
if best_binary_model[0] in ['SVM', 'Logistic Regression', 'Naive Bayes']:
    y_pred_best = best_model.predict(X_test_scaled)
else:
    y_pred_best = best_model.predict(X_test)

print(classification_report(y_test_bin, y_pred_best, target_names=['No Outbreak', 'Outbreak']))

# Confusion Matrix
cm = confusion_matrix(y_test_bin, y_pred_best)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['No Outbreak', 'Outbreak'], yticklabels=['No Outbreak', 'Outbreak'])
plt.title(f'Confusion Matrix - {best_binary_model[0]}')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

# Deep Learning Models
print("🧠 Building Deep Learning Models...")

try:
    # Prepare data for deep learning
    X_train_dl = X_train_scaled.astype(np.float32)
    X_test_dl = X_test_scaled.astype(np.float32)
    y_train_dl = y_train_bin.values.astype(np.float32)
    y_test_dl = y_test_bin.values.astype(np.float32)
    
    print(f"Deep learning data shape: {X_train_dl.shape}")
    
    # 1. Simple Neural Network for Binary Classification
    print("\n🔹 Building Simple Neural Network...")
    
    model_simple = Sequential([
        Dense(64, activation='relu', input_shape=(X_train_dl.shape[1],)),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(16, activation='relu'),
        Dropout(0.2),
        Dense(1, activation='sigmoid')
    ])
    
    model_simple.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    # Callbacks
    early_stopping = EarlyStopping(monitor='val_loss', patience=20, restore_best_weights=True)
    reduce_lr = ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=10, min_lr=1e-6)
    
    # Train the model
    history_simple = model_simple.fit(
        X_train_dl, y_train_dl,
        epochs=100,
        batch_size=16,
        validation_split=0.2,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )
    
    # Evaluate simple model
    y_pred_dl_simple = (model_simple.predict(X_test_dl) > 0.5).astype(int).flatten()
    y_pred_proba_dl_simple = model_simple.predict(X_test_dl).flatten()
    
    accuracy_dl_simple = accuracy_score(y_test_dl, y_pred_dl_simple)
    auc_dl_simple = roc_auc_score(y_test_dl, y_pred_proba_dl_simple)
    
    print(f"Simple NN - Accuracy: {accuracy_dl_simple:.3f}, AUC: {auc_dl_simple:.3f}")
    
    # 2. Advanced Neural Network with Regularization
    print("\n🔹 Building Advanced Neural Network...")
    
    model_advanced = Sequential([
        Dense(128, activation='relu', input_shape=(X_train_dl.shape[1],)),
        Dropout(0.4),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(16, activation='relu'),
        Dropout(0.2),
        Dense(8, activation='relu'),
        Dense(1, activation='sigmoid')
    ])
    
    model_advanced.compile(
        optimizer=Adam(learning_rate=0.001),
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    # Train advanced model
    history_advanced = model_advanced.fit(
        X_train_dl, y_train_dl,
        epochs=100,
        batch_size=16,
        validation_split=0.2,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )
    
    # Evaluate advanced model
    y_pred_dl_advanced = (model_advanced.predict(X_test_dl) > 0.5).astype(int).flatten()
    y_pred_proba_dl_advanced = model_advanced.predict(X_test_dl).flatten()
    
    accuracy_dl_advanced = accuracy_score(y_test_dl, y_pred_dl_advanced)
    auc_dl_advanced = roc_auc_score(y_test_dl, y_pred_proba_dl_advanced)
    
    print(f"Advanced NN - Accuracy: {accuracy_dl_advanced:.3f}, AUC: {auc_dl_advanced:.3f}")
    
    # Plot training history
    plt.figure(figsize=(15, 5))
    
    # Simple model history
    plt.subplot(1, 3, 1)
    plt.plot(history_simple.history['accuracy'], label='Train Accuracy')
    plt.plot(history_simple.history['val_accuracy'], label='Val Accuracy')
    plt.title('Simple NN - Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.subplot(1, 3, 2)
    plt.plot(history_simple.history['loss'], label='Train Loss')
    plt.plot(history_simple.history['val_loss'], label='Val Loss')
    plt.title('Simple NN - Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Advanced model history
    plt.subplot(1, 3, 3)
    plt.plot(history_advanced.history['accuracy'], label='Train Accuracy', linestyle='--')
    plt.plot(history_advanced.history['val_accuracy'], label='Val Accuracy', linestyle='--')
    plt.plot(history_advanced.history['loss'], label='Train Loss', linestyle=':')
    plt.plot(history_advanced.history['val_loss'], label='Val Loss', linestyle=':')
    plt.title('Advanced NN - Training History')
    plt.xlabel('Epoch')
    plt.ylabel('Metric')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Store deep learning results
    dl_results = {
        'Simple NN': {'accuracy': accuracy_dl_simple, 'auc': auc_dl_simple, 'model': model_simple},
        'Advanced NN': {'accuracy': accuracy_dl_advanced, 'auc': auc_dl_advanced, 'model': model_advanced}
    }
    
    print("\n✅ Deep Learning Models Completed!")
    
except Exception as e:
    print(f"⚠️ Deep Learning models failed: {e}")
    print("Continuing with traditional ML models only...")
    dl_results = {}

# Model Evaluation and Comparison
print("📈 Comprehensive Model Evaluation...")

# Combine all results
all_results = {}

# Add traditional ML results
for name, result in binary_results.items():
    all_results[name] = {
        'type': 'Traditional ML',
        'accuracy': result['accuracy'],
        'auc': result['auc'],
        'cv_mean': result['cv_mean'],
        'cv_std': result['cv_std']
    }

# Add deep learning results if available
if dl_results:
    for name, result in dl_results.items():
        all_results[name] = {
            'type': 'Deep Learning',
            'accuracy': result['accuracy'],
            'auc': result['auc'],
            'cv_mean': result['accuracy'],  # Use test accuracy as proxy
            'cv_std': 0.0
        }

# Create comparison DataFrame
comparison_df = pd.DataFrame(all_results).T
comparison_df = comparison_df.sort_values('cv_mean', ascending=False)

print("\n🏆 Model Performance Comparison:")
print("=" * 80)
print(f"{'Model':<20} {'Type':<15} {'Accuracy':<10} {'AUC':<8} {'CV Mean':<10} {'CV Std':<8}")
print("=" * 80)

for model_name, row in comparison_df.iterrows():
    print(f"{model_name:<20} {row['type']:<15} {row['accuracy']:<10.3f} {row['auc']:<8.3f} {row['cv_mean']:<10.3f} {row['cv_std']:<8.3f}")

# Visualize model comparison
plt.figure(figsize=(15, 10))

# Accuracy comparison
plt.subplot(2, 2, 1)
colors = ['skyblue' if t == 'Traditional ML' else 'lightcoral' for t in comparison_df['type']]
bars = plt.bar(range(len(comparison_df)), comparison_df['accuracy'], color=colors)
plt.title('Model Accuracy Comparison')
plt.xlabel('Models')
plt.ylabel('Accuracy')
plt.xticks(range(len(comparison_df)), comparison_df.index, rotation=45, ha='right')
plt.grid(True, alpha=0.3)

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{height:.3f}', ha='center', va='bottom', fontsize=8)

# AUC comparison
plt.subplot(2, 2, 2)
bars = plt.bar(range(len(comparison_df)), comparison_df['auc'], color=colors)
plt.title('Model AUC Comparison')
plt.xlabel('Models')
plt.ylabel('AUC Score')
plt.xticks(range(len(comparison_df)), comparison_df.index, rotation=45, ha='right')
plt.grid(True, alpha=0.3)

for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{height:.3f}', ha='center', va='bottom', fontsize=8)

# Cross-validation scores with error bars
plt.subplot(2, 2, 3)
plt.errorbar(range(len(comparison_df)), comparison_df['cv_mean'], 
             yerr=comparison_df['cv_std'], fmt='o', capsize=5, capthick=2)
plt.title('Cross-Validation Scores with Standard Deviation')
plt.xlabel('Models')
plt.ylabel('CV Score')
plt.xticks(range(len(comparison_df)), comparison_df.index, rotation=45, ha='right')
plt.grid(True, alpha=0.3)

# ROC Curves for top 3 models
plt.subplot(2, 2, 4)
top_3_models = comparison_df.head(3)

for model_name in top_3_models.index:
    if model_name in binary_results:
        model = binary_results[model_name]['model']
        if model_name in ['SVM', 'Logistic Regression', 'Naive Bayes']:
            y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
        else:
            y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        fpr, tpr, _ = roc_curve(y_test_bin, y_pred_proba)
        auc_score = roc_auc_score(y_test_bin, y_pred_proba)
        plt.plot(fpr, tpr, label=f'{model_name} (AUC = {auc_score:.3f})')

plt.plot([0, 1], [0, 1], 'k--', label='Random Classifier')
plt.title('ROC Curves - Top 3 Models')
plt.xlabel('False Positive Rate')
plt.ylabel('True Positive Rate')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Select final best model
final_best_model_name = comparison_df.index[0]
print(f"\n🎯 Final Best Model: {final_best_model_name}")
print(f"Performance: Accuracy = {comparison_df.loc[final_best_model_name, 'accuracy']:.3f}, "
      f"AUC = {comparison_df.loc[final_best_model_name, 'auc']:.3f}")

# Results and insights section - will be filled based on your guidance
print("💡 Results and insights section ready for your guidance...")

# Deployment section - will be filled based on your guidance
print("🚀 Deployment section ready for your guidance...")