'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'

interface WaterQualityData {
  temperature: number
  ph: number
  dissolved_oxygen: number
  bod: number
  nitrate_n: number
  fecal_coliform: number
  total_coliform: number
  fecal_streptococci: number
}

interface PredictionResult {
  risk_level: 'Low' | 'Medium' | 'High'
  probability: number
  confidence: number
  recommendations: string[]
}

const WaterQualityPredictor = () => {
  const [formData, setFormData] = useState<WaterQualityData>({
    temperature: 18,
    ph: 7.0,
    dissolved_oxygen: 19,
    bod: 5,
    nitrate_n: 2,
    fecal_coliform: 50,
    total_coliform: 100,
    fecal_streptococci: 10
  })

  const [prediction, setPrediction] = useState<PredictionResult | null>(null)
  const [loading, setLoading] = useState(false)

  const handleInputChange = (field: keyof WaterQualityData, value: number) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handlePredict = async () => {
    setLoading(true)
    try {
      // TODO: Replace with actual API call to your ML backend
      const response = await fetch('/api/predict', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        const result = await response.json()
        setPrediction(result)
      } else {
        // Mock prediction for now
        const mockPrediction: PredictionResult = {
          risk_level: formData.fecal_coliform > 100 ? 'High' : formData.fecal_coliform > 50 ? 'Medium' : 'Low',
          probability: Math.random() * 0.8 + 0.1,
          confidence: Math.random() * 0.3 + 0.7,
          recommendations: [
            'Monitor water quality regularly',
            'Implement water treatment measures',
            'Educate community about hygiene'
          ]
        }
        setPrediction(mockPrediction)
      }
    } catch (error) {
      console.error('Prediction error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'High': return 'text-red-600 bg-red-50 border-red-200'
      case 'Medium': return 'text-orange-600 bg-orange-50 border-orange-200'
      case 'Low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const waterQualityFields = [
    { key: 'temperature' as keyof WaterQualityData, label: 'Temperature (°C)', min: 0, max: 40, step: 0.1 },
    { key: 'ph' as keyof WaterQualityData, label: 'pH Level', min: 0, max: 14, step: 0.1 },
    { key: 'dissolved_oxygen' as keyof WaterQualityData, label: 'Dissolved Oxygen (mg/L)', min: 0, max: 30, step: 0.1 },
    { key: 'bod' as keyof WaterQualityData, label: 'BOD (mg/L)', min: 0, max: 100, step: 0.1 },
    { key: 'nitrate_n' as keyof WaterQualityData, label: 'Nitrate-N (mg/L)', min: 0, max: 50, step: 0.1 },
    { key: 'fecal_coliform' as keyof WaterQualityData, label: 'Fecal Coliform (CFU/100ml)', min: 0, max: 1000, step: 1 },
    { key: 'total_coliform' as keyof WaterQualityData, label: 'Total Coliform (CFU/100ml)', min: 0, max: 5000, step: 1 },
    { key: 'fecal_streptococci' as keyof WaterQualityData, label: 'Fecal Streptococci (CFU/100ml)', min: 0, max: 500, step: 1 }
  ]

  return (
    <section className="bg-gray-10 py-20">
      <div className="max-container padding-container">
        <motion.div
          className="mb-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="bold-40 lg:bold-52 text-gray-90 mb-6">
            Waterborne Disease Risk Predictor
          </h2>
          <p className="regular-16 text-gray-50 max-w-3xl mx-auto">
            Enter water quality parameters to predict the risk of waterborne disease outbreaks using our AI model trained on Northeast India data.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Input Form */}
          <motion.div
            className="bg-white rounded-2xl p-8 border border-gray-20"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 className="bold-24 text-gray-90 mb-6">Water Quality Parameters</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              {waterQualityFields.map((field, index) => (
                <motion.div
                  key={field.key}
                  className="space-y-2"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <label className="block text-sm font-medium text-gray-70">
                    {field.label}
                  </label>
                  <input
                    type="number"
                    min={field.min}
                    max={field.max}
                    step={field.step}
                    value={formData[field.key]}
                    onChange={(e) => handleInputChange(field.key, parseFloat(e.target.value) || 0)}
                    className="w-full px-4 py-3 border border-gray-30 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                  />
                </motion.div>
              ))}
            </div>

            <motion.button
              onClick={handlePredict}
              disabled={loading}
              className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-4 px-6 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {loading ? 'Analyzing...' : 'Predict Disease Risk'}
            </motion.button>
          </motion.div>

          {/* Results */}
          <motion.div
            className="bg-white rounded-2xl p-8 border border-gray-20"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h3 className="bold-24 text-gray-90 mb-6">Prediction Results</h3>
            
            {prediction ? (
              <div className="space-y-6">
                <div className={`p-6 rounded-xl border-2 ${getRiskColor(prediction.risk_level)}`}>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="bold-20">Risk Level: {prediction.risk_level}</h4>
                    <span className="text-2xl">
                      {prediction.risk_level === 'High' ? '🔴' : 
                       prediction.risk_level === 'Medium' ? '🟡' : '🟢'}
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Probability:</span>
                      <div className="text-lg font-bold">{(prediction.probability * 100).toFixed(1)}%</div>
                    </div>
                    <div>
                      <span className="font-medium">Confidence:</span>
                      <div className="text-lg font-bold">{(prediction.confidence * 100).toFixed(1)}%</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="bold-16 text-gray-90 mb-3">Recommendations:</h5>
                  <ul className="space-y-2">
                    {prediction.recommendations.map((rec, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-primary-600 mt-1">•</span>
                        <span className="regular-14 text-gray-70">{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🔬</span>
                </div>
                <p className="regular-16 text-gray-50">
                  Enter water quality parameters and click "Predict Disease Risk" to see results.
                </p>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default WaterQualityPredictor
