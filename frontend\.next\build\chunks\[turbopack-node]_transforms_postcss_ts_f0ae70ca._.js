module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "build/chunks/e2701_1374c6d8._.js",
  "build/chunks/[root-of-the-server]__87864f56._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];