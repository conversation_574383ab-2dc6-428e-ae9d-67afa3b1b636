{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/About.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst About = () => {\r\n  return (\r\n    <section className=\"bg-white py-20\">\r\n      <div className=\"max-container padding-container\">\r\n        <div className=\"max-w-4xl\">\r\n          <motion.h2\r\n            className=\"bold-40 lg:bold-52 mb-6 text-gray-90 text-left\"\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.8, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            Understanding Waterborne Diseases\r\n          </motion.h2>\r\n          <motion.p\r\n            className=\"regular-16 text-gray-50 mb-6 leading-relaxed text-left\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.4 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            Waterborne diseases are caused by pathogenic microorganisms that are transmitted in water.\r\n            These diseases can be spread while bathing, washing, drinking water, or by eating food exposed\r\n            to contaminated water.\r\n          </motion.p>\r\n          <motion.p\r\n            className=\"regular-16 text-gray-50 mb-8 leading-relaxed text-left\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.6 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            Common waterborne diseases include cholera, typhoid, hepatitis A, diarrhea, and dysentery.\r\n            Prevention is key to avoiding these diseases through proper water treatment, sanitation, and hygiene practices.\r\n          </motion.p>\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-2xl\">\r\n            <div className=\"flex items-center gap-3 bg-success-50 p-4 rounded-xl\">\r\n              <div className=\"w-3 h-3 bg-success-100 rounded-full\"></div>\r\n              <span className=\"regular-14 text-gray-90 font-medium\">Prevention</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-3 bg-primary-50 p-4 rounded-xl\">\r\n              <div className=\"w-3 h-3 bg-primary-300 rounded-full\"></div>\r\n              <span className=\"regular-14 text-gray-90 font-medium\">Awareness</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-3 bg-accent-50 p-4 rounded-xl\">\r\n              <div className=\"w-3 h-3 bg-accent-100 rounded-full\"></div>\r\n              <span className=\"regular-14 text-gray-90 font-medium\">Treatment</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default About\r\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKA,MAAM,QAAQ;IACZ,qBACE,4TAAC;QAAQ,WAAU;kBACjB,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCACb,4TAAC,kRAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCACxB;;;;;;kCAGD,4TAAC,kRAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCACxB;;;;;;kCAKD,4TAAC,kRAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;kCACxB;;;;;;kCAID,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;;;;;kDACf,4TAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;0CAExD,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;;;;;kDACf,4TAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;0CAExD,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;;;;;kDACf,4TAAC;wCAAK,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpE;uCAEe", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/GraphsandCharts.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport Image from 'next/image'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst GraphsandCharts = () => {\r\n  const neStats = [\r\n    { number: \"10%\", label: \"Diarrhea prevalence in Meghalaya\", state: \"Meghalaya\", icon: \"/location.svg\" },\r\n    { number: \"4.8%\", label: \"ARI prevalence in Meghalaya\", state: \"Meghalaya\", icon: \"/location.svg\" },\r\n    { number: \"23%\", label: \"Fever prevalence in Meghalaya\", state: \"Meghalaya\", icon: \"/calendar.svg\" },\r\n    { number: \"6%\", label: \"Overall diarrhea in Northeast\", state: \"Northeast\", icon: \"/tech.svg\" }\r\n  ]\r\n\r\n  const stateData = [\r\n    { state: \"Meghalaya\", diarrhea: 10.0, fever: 23.0, ari: 4.8 },\r\n    { state: \"Tripura\", diarrhea: 6.0, fever: 15.0, ari: 1.5 },\r\n    { state: \"Assam\", diarrhea: 5.5, fever: 16.0, ari: 2.5 },\r\n    { state: \"Arunachal Pradesh\", diarrhea: 5.5, fever: 9.0, ari: 2.1 },\r\n    { state: \"Sikkim\", diarrhea: 5.0, fever: 12.0, ari: 0.8 },\r\n    { state: \"Manipur\", diarrhea: 5.0, fever: 14.0, ari: 1.2 },\r\n    { state: \"Mizoram\", diarrhea: 4.3, fever: 13.0, ari: 0.9 },\r\n    { state: \"Nagaland\", diarrhea: 3.4, fever: 11.0, ari: 1.0 }\r\n  ]\r\n\r\n  return (\r\n    <section id=\"statistics\" className=\"bg-white py-20\">\r\n      <div className=\"max-container padding-container\">\r\n        <div className=\"mb-16\">\r\n          <h2 className=\"bold-40 lg:bold-52 text-gray-90 mb-6 text-left\">\r\n            Waterborne Diseases in Northeast India\r\n          </h2>\r\n          <p className=\"regular-16 text-gray-50 max-w-3xl text-left\">\r\n            Based on NFHS-5 (2019-21) data, Northeast India shows significant variations in waterborne disease prevalence across states.\r\n            Meghalaya consistently shows the highest rates across all major waterborne illnesses.\r\n          </p>\r\n        </div>\r\n\r\n        {/* Key Statistics Cards */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\r\n          {neStats.map((stat, index) => (\r\n            <motion.div\r\n              key={index}\r\n              className=\"bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-6 text-center border border-primary-200 hover:shadow-lg transition-all\"\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              whileHover={{ scale: 1.02 }}\r\n            >\r\n              <motion.div\r\n                className=\"w-12 h-12 bg-primary-300 rounded-full flex items-center justify-center mx-auto mb-3\"\r\n                initial={{ scale: 0 }}\r\n                whileInView={{ scale: 1 }}\r\n                transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                <Image\r\n                  src={stat.icon}\r\n                  alt={stat.label}\r\n                  width={24}\r\n                  height={24}\r\n                  className=\"filter brightness-0 invert\"\r\n                />\r\n              </motion.div>\r\n              <motion.h3\r\n                className=\"bold-28 text-primary-700 mb-2\"\r\n                initial={{ opacity: 0 }}\r\n                whileInView={{ opacity: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                {stat.number}\r\n              </motion.h3>\r\n              <motion.p\r\n                className=\"regular-14 text-gray-70 mb-1 leading-tight\"\r\n                initial={{ opacity: 0 }}\r\n                whileInView={{ opacity: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}\r\n                viewport={{ once: true }}\r\n              >\r\n                {stat.label}\r\n              </motion.p>\r\n              <span className=\"text-xs text-primary-600 font-medium\">{stat.state}</span>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* State-wise Data Table */}\r\n        <div className=\"bg-gray-10 rounded-2xl p-8 border border-gray-20 mb-12\">\r\n          <h3 className=\"bold-24 text-gray-90 mb-6 text-center\">\r\n            State-wise Waterborne Disease Prevalence (%)\r\n          </h3>\r\n          <div className=\"overflow-x-auto\">\r\n            <table className=\"w-full\">\r\n              <thead>\r\n                <tr className=\"border-b border-gray-30\">\r\n                  <th className=\"text-left py-4 px-4 bold-16 text-gray-90\">State</th>\r\n                  <th className=\"text-center py-4 px-4 bold-16 text-gray-90\">Diarrhea</th>\r\n                  <th className=\"text-center py-4 px-4 bold-16 text-gray-90\">Fever</th>\r\n                  <th className=\"text-center py-4 px-4 bold-16 text-gray-90\">ARI</th>\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {stateData.map((state, index) => (\r\n                  <motion.tr\r\n                    key={state.state}\r\n                    className=\"border-b border-gray-20 hover:bg-primary-50 transition-colors\"\r\n                    initial={{ opacity: 0, x: -20 }}\r\n                    whileInView={{ opacity: 1, x: 0 }}\r\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                    viewport={{ once: true }}\r\n                  >\r\n                    <td className=\"py-4 px-4 regular-16 text-gray-90 font-medium\">{state.state}</td>\r\n                    <td className=\"py-4 px-4 text-center\">\r\n                      <span className={`regular-16 font-medium ${state.diarrhea >= 8 ? 'text-red-600' : state.diarrhea >= 5 ? 'text-orange-600' : 'text-green-600'}`}>\r\n                        {state.diarrhea}%\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"py-4 px-4 text-center\">\r\n                      <span className={`regular-16 font-medium ${state.fever >= 20 ? 'text-red-600' : state.fever >= 15 ? 'text-orange-600' : 'text-green-600'}`}>\r\n                        {state.fever}%\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"py-4 px-4 text-center\">\r\n                      <span className={`regular-16 font-medium ${state.ari >= 3 ? 'text-red-600' : state.ari >= 2 ? 'text-orange-600' : 'text-green-600'}`}>\r\n                        {state.ari}%\r\n                      </span>\r\n                    </td>\r\n                  </motion.tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Key Insights */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n          <motion.div\r\n            className=\"bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 border border-red-200\"\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <div className=\"w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mb-4\">\r\n              <span className=\"text-white bold-16\">!</span>\r\n            </div>\r\n            <h4 className=\"bold-18 text-gray-90 mb-3\">Highest Risk</h4>\r\n            <p className=\"regular-14 text-gray-70 mb-2\">\r\n              <strong>Meghalaya</strong> shows the highest prevalence across all waterborne diseases:\r\n            </p>\r\n            <ul className=\"text-sm text-gray-70 space-y-1\">\r\n              <li>• Diarrhea: 10% (highest)</li>\r\n              <li>• Fever: 23% (highest)</li>\r\n              <li>• ARI: 4.8% (highest)</li>\r\n            </ul>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200\"\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.2 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <div className=\"w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mb-4\">\r\n              <span className=\"text-white bold-16\">⚠</span>\r\n            </div>\r\n            <h4 className=\"bold-18 text-gray-90 mb-3\">Contributing Factors</h4>\r\n            <p className=\"regular-14 text-gray-70 mb-2\">\r\n              Research shows higher prevalence linked to:\r\n            </p>\r\n            <ul className=\"text-sm text-gray-70 space-y-1\">\r\n              <li>• Poor sanitation facilities</li>\r\n              <li>• Lack of clean water access</li>\r\n              <li>• Lower socioeconomic status</li>\r\n              <li>• Rural living conditions</li>\r\n            </ul>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200\"\r\n            initial={{ opacity: 0, y: 30 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6, delay: 0.4 }}\r\n            viewport={{ once: true }}\r\n          >\r\n            <div className=\"w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mb-4\">\r\n              <span className=\"text-white bold-16\">✓</span>\r\n            </div>\r\n            <h4 className=\"bold-18 text-gray-90 mb-3\">Prevention Works</h4>\r\n            <p className=\"regular-14 text-gray-70 mb-2\">\r\n              States with better outcomes show:\r\n            </p>\r\n            <ul className=\"text-sm text-gray-70 space-y-1\">\r\n              <li>• Improved water treatment</li>\r\n              <li>• Better sanitation coverage</li>\r\n              <li>• Health education programs</li>\r\n              <li>• Vaccination initiatives</li>\r\n            </ul>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default GraphsandCharts\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,kBAAkB;IACtB,MAAM,UAAU;QACd;YAAE,QAAQ;YAAO,OAAO;YAAoC,OAAO;YAAa,MAAM;QAAgB;QACtG;YAAE,QAAQ;YAAQ,OAAO;YAA+B,OAAO;YAAa,MAAM;QAAgB;QAClG;YAAE,QAAQ;YAAO,OAAO;YAAiC,OAAO;YAAa,MAAM;QAAgB;QACnG;YAAE,QAAQ;YAAM,OAAO;YAAiC,OAAO;YAAa,MAAM;QAAY;KAC/F;IAED,MAAM,YAAY;QAChB;YAAE,OAAO;YAAa,UAAU;YAAM,OAAO;YAAM,KAAK;QAAI;QAC5D;YAAE,OAAO;YAAW,UAAU;YAAK,OAAO;YAAM,KAAK;QAAI;QACzD;YAAE,OAAO;YAAS,UAAU;YAAK,OAAO;YAAM,KAAK;QAAI;QACvD;YAAE,OAAO;YAAqB,UAAU;YAAK,OAAO;YAAK,KAAK;QAAI;QAClE;YAAE,OAAO;YAAU,UAAU;YAAK,OAAO;YAAM,KAAK;QAAI;QACxD;YAAE,OAAO;YAAW,UAAU;YAAK,OAAO;YAAM,KAAK;QAAI;QACzD;YAAE,OAAO;YAAW,UAAU;YAAK,OAAO;YAAM,KAAK;QAAI;QACzD;YAAE,OAAO;YAAY,UAAU;YAAK,OAAO;YAAM,KAAK;QAAI;KAC3D;IAED,qBACE,4TAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,4TAAC;YAAI,WAAU;;8BACb,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,4TAAC;4BAAE,WAAU;sCAA8C;;;;;;;;;;;;8BAO7D,4TAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,4TAAC,kRAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;4BAAK;;8CAE1B,4TAAC,kRAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,aAAa;wCAAE,OAAO;oCAAE;oCACxB,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,UAAU;wCAAE,MAAM;oCAAK;8CAEvB,cAAA,4TAAC,sNAAK;wCACJ,KAAK,KAAK,IAAI;wCACd,KAAK,KAAK,KAAK;wCACf,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAGd,4TAAC,kRAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,aAAa;wCAAE,SAAS;oCAAE;oCAC1B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,UAAU;wCAAE,MAAM;oCAAK;8CAEtB,KAAK,MAAM;;;;;;8CAEd,4TAAC,kRAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,aAAa;wCAAE,SAAS;oCAAE;oCAC1B,YAAY;wCAAE,UAAU;wCAAK,OAAO,MAAM,QAAQ;oCAAI;oCACtD,UAAU;wCAAE,MAAM;oCAAK;8CAEtB,KAAK,KAAK;;;;;;8CAEb,4TAAC;oCAAK,WAAU;8CAAwC,KAAK,KAAK;;;;;;;2BAzC7D;;;;;;;;;;8BA+CX,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAM,WAAU;;kDACf,4TAAC;kDACC,cAAA,4TAAC;4CAAG,WAAU;;8DACZ,4TAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,4TAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,4TAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,4TAAC;oDAAG,WAAU;8DAA6C;;;;;;;;;;;;;;;;;kDAG/D,4TAAC;kDACE,UAAU,GAAG,CAAC,CAAC,OAAO,sBACrB,4TAAC,kRAAM,CAAC,EAAE;gDAER,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;;kEAEvB,4TAAC;wDAAG,WAAU;kEAAiD,MAAM,KAAK;;;;;;kEAC1E,4TAAC;wDAAG,WAAU;kEACZ,cAAA,4TAAC;4DAAK,WAAW,CAAC,uBAAuB,EAAE,MAAM,QAAQ,IAAI,IAAI,iBAAiB,MAAM,QAAQ,IAAI,IAAI,oBAAoB,kBAAkB;;gEAC3I,MAAM,QAAQ;gEAAC;;;;;;;;;;;;kEAGpB,4TAAC;wDAAG,WAAU;kEACZ,cAAA,4TAAC;4DAAK,WAAW,CAAC,uBAAuB,EAAE,MAAM,KAAK,IAAI,KAAK,iBAAiB,MAAM,KAAK,IAAI,KAAK,oBAAoB,kBAAkB;;gEACvI,MAAM,KAAK;gEAAC;;;;;;;;;;;;kEAGjB,4TAAC;wDAAG,WAAU;kEACZ,cAAA,4TAAC;4DAAK,WAAW,CAAC,uBAAuB,EAAE,MAAM,GAAG,IAAI,IAAI,iBAAiB,MAAM,GAAG,IAAI,IAAI,oBAAoB,kBAAkB;;gEACjI,MAAM,GAAG;gEAAC;;;;;;;;;;;;;+CApBV,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BA+B5B,4TAAC;oBAAI,WAAU;;sCACb,4TAAC,kRAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,4TAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,4TAAC;oCAAE,WAAU;;sDACX,4TAAC;sDAAO;;;;;;wCAAkB;;;;;;;8CAE5B,4TAAC;oCAAG,WAAU;;sDACZ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,4TAAC,kRAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,4TAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,4TAAC;oCAAE,WAAU;8CAA+B;;;;;;8CAG5C,4TAAC;oCAAG,WAAU;;sDACZ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,4TAAC,kRAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;8CAEvC,4TAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,4TAAC;oCAAE,WAAU;8CAA+B;;;;;;8CAG5C,4TAAC;oCAAG,WAAU;;sDACZ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;sDACJ,4TAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/Button.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\ntype ButtonProps={\r\n    type: 'button' | 'submit';\r\n    title: string;\r\n    icon?: string;\r\n    variant: string\r\n\r\n}\r\nconst Button = ({type,title,icon,variant}: ButtonProps) => {\r\n  return (\r\n    <button type={type}\r\n    className={`flexCenter gap-3 rounded-xl ${variant} transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg`}>\r\n        {icon && <Image src={icon} alt={title} width={24} height={24}/> }\r\n        <label className=\"bold-16 whitespace-nowrap cursor-pointer\">{title}</label>\r\n    </button>\r\n  )\r\n}\r\n\r\nexport default Button\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AASA,MAAM,SAAS,CAAC,EAAC,IAAI,EAAC,KAAK,EAAC,IAAI,EAAC,OAAO,EAAc;IACpD,qBACE,4TAAC;QAAO,MAAM;QACd,WAAW,CAAC,4BAA4B,EAAE,QAAQ,gFAAgF,CAAC;;YAC9H,sBAAQ,4TAAC,sNAAK;gBAAC,KAAK;gBAAM,KAAK;gBAAO,OAAO;gBAAI,QAAQ;;;;;;0BAC1D,4TAAC;gBAAM,WAAU;0BAA4C;;;;;;;;;;;;AAGrE;uCAEe", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/Intro.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React from 'react'\r\nimport <PERSON><PERSON> from './Button'\r\nimport Link from 'next/link'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst Intro = () => {\r\n  return (\r\n    <section className=\"min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100 relative\">\r\n        <div className=\"max-container padding-container flex flex-col gap-12 py-20 md:gap-16 lg:py-32 xl:flex-row items-center\">\r\n\r\n        <div className='relative z-20 flex flex-1 flex-col xl:w-1/2'>\r\n        <motion.h1\r\n          className=\"bold-52 lg:bold-64 text-gray-90 mb-4\"\r\n          initial={{ opacity: 0, y: 30 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.2 }}\r\n        >\r\n            Waterborne Diseases Awareness\r\n        </motion.h1>\r\n\r\n        <motion.p\r\n          className=\"regular-18 text-primary-600 font-medium mb-6\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.4 }}\r\n        >\r\n            Specialized healthcare guidance for Northeast India\r\n        </motion.p>\r\n\r\n        <motion.p\r\n          className=\"regular-16 text-gray-50 xl:max-w-[520px] mb-8 leading-relaxed\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.8, delay: 0.6 }}\r\n        >\r\n            Waterborne diseases are illnesses caused by pathogens (such as bacteria, viruses, or parasites) that are transmitted to humans through contaminated water. These diseases can affect various organs and systems in the body, leading to a range of symptoms and health complications. We aim to raise awareness about waterborne diseases and their prevention, with a special focus on the unique challenges faced in Northeast India.\r\n        </motion.p>\r\n\r\n        <motion.div\r\n          className=\"flex flex-col sm:flex-row gap-4 mb-8\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.8 }}\r\n        >\r\n            <Link href=\"/get-started\">\r\n              <Button type=\"button\" title=\"Get Started\" variant=\"btn_primary_dark\"/>\r\n            </Link>\r\n            <Button type=\"button\" title=\"Learn More\" variant=\"btn_white_text\"/>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          className=\"flex items-center gap-6 text-sm text-gray-50\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 1.0 }}\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"w-3 h-3 bg-primary-300 rounded-full\"></div>\r\n            <span>Symptom Analysis</span>\r\n          </div>\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"w-3 h-3 bg-accent-100 rounded-full\"></div>\r\n            <span>24/7 Support</span>\r\n          </div>\r\n        </motion.div>\r\n        </div>\r\n        </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default Intro\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AALA;;;;;AAOA,MAAM,QAAQ;IACZ,qBACE,4TAAC;QAAQ,WAAU;kBACf,cAAA,4TAAC;YAAI,WAAU;sBAEf,cAAA,4TAAC;gBAAI,WAAU;;kCACf,4TAAC,kRAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCACzC;;;;;;kCAID,4TAAC,kRAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCACzC;;;;;;kCAID,4TAAC,kRAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCACzC;;;;;;kCAID,4TAAC,kRAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAEtC,4TAAC,qPAAI;gCAAC,MAAK;0CACT,cAAA,4TAAC,8MAAM;oCAAC,MAAK;oCAAS,OAAM;oCAAc,SAAQ;;;;;;;;;;;0CAEpD,4TAAC,8MAAM;gCAAC,MAAK;gCAAS,OAAM;gCAAa,SAAQ;;;;;;;;;;;;kCAGrD,4TAAC,kRAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;;;;;kDACf,4TAAC;kDAAK;;;;;;;;;;;;0CAER,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;;;;;kDACf,4TAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;uCAEe", "debugId": null}}]}