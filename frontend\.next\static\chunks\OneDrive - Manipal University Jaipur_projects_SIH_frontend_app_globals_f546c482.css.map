{"version": 3, "sources": [], "sections": [{"offset": {"line": 2, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/app/globals.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');\r\n\r\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\r\n\r\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\r\n\r\n/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*/\r\n\r\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\r\n\r\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\r\n\r\n::before,\n::after {\n  --tw-content: '';\n}\r\n\r\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\r\n\r\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\r\n\r\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\r\n\r\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\r\n\r\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\r\n\r\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\r\n\r\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\r\n\r\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\r\n\r\n/*\nRemove the default font size and weight for headings.\n*/\r\n\r\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\r\n\r\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\r\n\r\na {\n  color: inherit;\n  text-decoration: inherit;\n}\r\n\r\n/*\nAdd the correct font weight in Edge and Safari.\n*/\r\n\r\nb,\nstrong {\n  font-weight: bolder;\n}\r\n\r\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\r\n\r\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\r\n\r\n/*\nAdd the correct font size in all browsers.\n*/\r\n\r\nsmall {\n  font-size: 80%;\n}\r\n\r\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\r\n\r\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\r\n\r\nsub {\n  bottom: -0.25em;\n}\r\n\r\nsup {\n  top: -0.5em;\n}\r\n\r\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\r\n\r\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\r\n\r\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\r\n\r\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\r\n\r\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\r\n\r\nbutton,\nselect {\n  text-transform: none;\n}\r\n\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\r\n\r\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\r\n\r\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\r\n\r\n:-moz-focusring {\n  outline: auto;\n}\r\n\r\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\r\n\r\n:-moz-ui-invalid {\n  box-shadow: none;\n}\r\n\r\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\r\n\r\nprogress {\n  vertical-align: baseline;\n}\r\n\r\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\r\n\r\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\r\n\r\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\r\n\r\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\r\n\r\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\r\n\r\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\r\n\r\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\r\n\r\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\r\n\r\n/*\nAdd the correct display in Chrome and Safari.\n*/\r\n\r\nsummary {\n  display: list-item;\n}\r\n\r\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\r\n\r\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\r\n\r\nfieldset {\n  margin: 0;\n  padding: 0;\n}\r\n\r\nlegend {\n  padding: 0;\n}\r\n\r\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\r\n\r\n/*\nReset default styling for dialogs.\n*/\r\n\r\ndialog {\n  padding: 0;\n}\r\n\r\n/*\nPrevent resizing textareas horizontally by default.\n*/\r\n\r\ntextarea {\n  resize: vertical;\n}\r\n\r\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\r\n\r\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\n\r\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\r\n\r\n/*\nSet the default cursor for buttons.\n*/\r\n\r\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\r\n\r\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\r\n\r\n:disabled {\n  cursor: default;\n}\r\n\r\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\r\n\r\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\r\n\r\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\r\n\r\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\r\n\r\n/* Make elements with the HTML hidden attribute stay hidden by default */\r\n\r\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n.btn_white_text{\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(125 221 232 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  padding-left: 2rem;\n  padding-right: 2rem;\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.btn_white_text:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(179 235 242 / var(--tw-bg-opacity, 1));\n}\r\n.btn_primary_dark{\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 168 183 / var(--tw-bg-opacity, 1));\n  padding-left: 2rem;\n  padding-right: 2rem;\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.btn_primary_dark:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 145 158 / var(--tw-bg-opacity, 1));\n}\r\n.max-container{\n  margin-left: auto;\n  margin-right: auto;\n  max-width: 1440px;\n}\r\n.padding-container{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n@media (min-width: 1024px){\r\n\r\n  .padding-container{\n    padding-left: 5rem;\n    padding-right: 5rem;\n  }\n}\r\n@media (min-width: 1680px){\r\n\r\n  .padding-container{\n    padding-left: 0px;\n    padding-right: 0px;\n  }\n}\r\n.flexCenter{\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\r\n.flexBetween{\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\r\n/* Typography */\r\n.regular-18{\n  font-size: 18px;\n  font-weight: 400;\n}\r\n.regular-16{\n  font-size: 16px;\n  font-weight: 400;\n}\r\n.regular-14{\n  font-size: 14px;\n  font-weight: 400;\n}\r\n.bold-52{\n  font-size: 52px;\n  font-weight: 700;\n  line-height: 120%;\n}\r\n.bold-40{\n  font-size: 40px;\n  font-weight: 700;\n  line-height: 120%;\n}\r\n.bold-18{\n  font-size: 18px;\n  font-weight: 700;\n}\r\n.bold-16{\n  font-size: 16px;\n  font-weight: 700;\n}\r\n/* Hero */\r\n/* Camp */\r\n/* Feature */\r\n/* Get App */\r\n.absolute{\n  position: absolute;\n}\r\n.relative{\n  position: relative;\n}\r\n.-bottom-1{\n  bottom: -0.25rem;\n}\r\n.-right-1{\n  right: -0.25rem;\n}\r\n.bottom-8{\n  bottom: 2rem;\n}\r\n.left-8{\n  left: 2rem;\n}\r\n.right-8{\n  right: 2rem;\n}\r\n.z-20{\n  z-index: 20;\n}\r\n.z-30{\n  z-index: 30;\n}\r\n.mx-auto{\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.mb-1{\n  margin-bottom: 0.25rem;\n}\r\n.mb-12{\n  margin-bottom: 3rem;\n}\r\n.mb-16{\n  margin-bottom: 4rem;\n}\r\n.mb-2{\n  margin-bottom: 0.5rem;\n}\r\n.mb-3{\n  margin-bottom: 0.75rem;\n}\r\n.mb-4{\n  margin-bottom: 1rem;\n}\r\n.mb-6{\n  margin-bottom: 1.5rem;\n}\r\n.mb-8{\n  margin-bottom: 2rem;\n}\r\n.ml-2{\n  margin-left: 0.5rem;\n}\r\n.mt-12{\n  margin-top: 3rem;\n}\r\n.mt-2{\n  margin-top: 0.5rem;\n}\r\n.mt-4{\n  margin-top: 1rem;\n}\r\n.block{\n  display: block;\n}\r\n.inline-block{\n  display: inline-block;\n}\r\n.flex{\n  display: flex;\n}\r\n.inline-flex{\n  display: inline-flex;\n}\r\n.table{\n  display: table;\n}\r\n.grid{\n  display: grid;\n}\r\n.hidden{\n  display: none;\n}\r\n.h-10{\n  height: 2.5rem;\n}\r\n.h-12{\n  height: 3rem;\n}\r\n.h-20{\n  height: 5rem;\n}\r\n.h-24{\n  height: 6rem;\n}\r\n.h-3{\n  height: 0.75rem;\n}\r\n.h-4{\n  height: 1rem;\n}\r\n.h-full{\n  height: 100%;\n}\r\n.min-h-\\[300px\\]{\n  min-height: 300px;\n}\r\n.min-h-screen{\n  min-height: 100vh;\n}\r\n.w-10{\n  width: 2.5rem;\n}\r\n.w-12{\n  width: 3rem;\n}\r\n.w-20{\n  width: 5rem;\n}\r\n.w-24{\n  width: 6rem;\n}\r\n.w-3{\n  width: 0.75rem;\n}\r\n.w-32{\n  width: 8rem;\n}\r\n.w-4{\n  width: 1rem;\n}\r\n.w-full{\n  width: 100%;\n}\r\n.max-w-2xl{\n  max-width: 42rem;\n}\r\n.max-w-3xl{\n  max-width: 48rem;\n}\r\n.max-w-4xl{\n  max-width: 56rem;\n}\r\n.flex-1{\n  flex: 1 1 0%;\n}\r\n.transform{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.cursor-pointer{\n  cursor: pointer;\n}\r\n.resize-none{\n  resize: none;\n}\r\n.grid-cols-1{\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\r\n.flex-col{\n  flex-direction: column;\n}\r\n.items-center{\n  align-items: center;\n}\r\n.justify-center{\n  justify-content: center;\n}\r\n.justify-between{\n  justify-content: space-between;\n}\r\n.gap-12{\n  gap: 3rem;\n}\r\n.gap-2{\n  gap: 0.5rem;\n}\r\n.gap-3{\n  gap: 0.75rem;\n}\r\n.gap-4{\n  gap: 1rem;\n}\r\n.gap-6{\n  gap: 1.5rem;\n}\r\n.gap-8{\n  gap: 2rem;\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-20 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\r\n.overflow-hidden{\n  overflow: hidden;\n}\r\n.overflow-x-auto{\n  overflow-x: auto;\n}\r\n.whitespace-nowrap{\n  white-space: nowrap;\n}\r\n.rounded{\n  border-radius: 0.25rem;\n}\r\n.rounded-2xl{\n  border-radius: 1rem;\n}\r\n.rounded-full{\n  border-radius: 9999px;\n}\r\n.rounded-lg{\n  border-radius: 0.5rem;\n}\r\n.rounded-xl{\n  border-radius: 0.75rem;\n}\r\n.border{\n  border-width: 1px;\n}\r\n.border-2{\n  border-width: 2px;\n}\r\n.border-b{\n  border-bottom-width: 1px;\n}\r\n.border-b-2{\n  border-bottom-width: 2px;\n}\r\n.border-t{\n  border-top-width: 1px;\n}\r\n.border-gray-20{\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-30{\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\r\n.border-green-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\r\n.border-orange-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\n}\r\n.border-primary-100{\n  --tw-border-opacity: 1;\n  border-color: rgb(159 231 240 / var(--tw-border-opacity, 1));\n}\r\n.border-primary-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(125 221 232 / var(--tw-border-opacity, 1));\n}\r\n.border-primary-600{\n  --tw-border-opacity: 1;\n  border-color: rgb(20 168 183 / var(--tw-border-opacity, 1));\n}\r\n.border-red-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\r\n.border-white{\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\r\n.bg-accent-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));\n}\r\n.bg-accent-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-10{\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(159 231 240 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-300{\n  --tw-bg-opacity: 1;\n  background-color: rgb(91 211 224 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-400{\n  --tw-bg-opacity: 1;\n  background-color: rgb(57 201 216 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(179 235 242 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(23 191 208 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary-700{\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 145 158 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\r\n.bg-success-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));\n}\r\n.bg-success-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));\n}\r\n.bg-white{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-white\\/10{\n  background-color: rgb(255 255 255 / 0.1);\n}\r\n.bg-white\\/90{\n  background-color: rgb(255 255 255 / 0.9);\n}\r\n.bg-gradient-to-br{\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\r\n.from-green-50{\n  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-orange-50{\n  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-primary-50{\n  --tw-gradient-from: #B3EBF2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(179 235 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-red-50{\n  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.via-white{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.to-green-100{\n  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);\n}\r\n.to-orange-100{\n  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);\n}\r\n.to-primary-100{\n  --tw-gradient-to: #9FE7F0 var(--tw-gradient-to-position);\n}\r\n.to-red-100{\n  --tw-gradient-to: #fee2e2 var(--tw-gradient-to-position);\n}\r\n.p-4{\n  padding: 1rem;\n}\r\n.p-6{\n  padding: 1.5rem;\n}\r\n.p-8{\n  padding: 2rem;\n}\r\n.px-12{\n  padding-left: 3rem;\n  padding-right: 3rem;\n}\r\n.px-4{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-6{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.py-16{\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2{\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-20{\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\r\n.py-4{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.py-5{\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\r\n.pb-1\\.5{\n  padding-bottom: 0.375rem;\n}\r\n.pt-6{\n  padding-top: 1.5rem;\n}\r\n.pt-8{\n  padding-top: 2rem;\n}\r\n.text-left{\n  text-align: left;\n}\r\n.text-center{\n  text-align: center;\n}\r\n.text-2xl{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-sm{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xs{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-medium{\n  font-weight: 500;\n}\r\n.font-normal{\n  font-weight: 400;\n}\r\n.font-semibold{\n  font-weight: 600;\n}\r\n.italic{\n  font-style: italic;\n}\r\n.leading-relaxed{\n  line-height: 1.625;\n}\r\n.leading-tight{\n  line-height: 1.25;\n}\r\n.text-gray-300{\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-50{\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-90{\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\r\n.text-green-600{\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-600{\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-100{\n  --tw-text-opacity: 1;\n  color: rgb(159 231 240 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-200{\n  --tw-text-opacity: 1;\n  color: rgb(125 221 232 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-600{\n  --tw-text-opacity: 1;\n  color: rgb(20 168 183 / var(--tw-text-opacity, 1));\n}\r\n.text-primary-700{\n  --tw-text-opacity: 1;\n  color: rgb(17 145 158 / var(--tw-text-opacity, 1));\n}\r\n.text-red-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-md{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.brightness-0{\n  --tw-brightness: brightness(0);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.invert{\n  --tw-invert: invert(100%);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur-md{\n  --tw-backdrop-blur: blur(12px);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity{\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.duration-300{\n  transition-duration: 300ms;\n}\r\n\r\n*{\n  margin: 0px;\n  box-sizing: border-box;\n  padding: 0px;\n}\r\n\r\nbody {\r\n  font-family: 'Inter', sans-serif;\r\n}\r\n\r\n/* Hide scrollbar */\r\n.hide-scrollbar::-webkit-scrollbar { display: none; }\r\n.hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }\r\n@media (min-width: 1024px){\r\n  .lg\\:bold-64{\n    font-size: 64px;\n    font-weight: 700;\n    line-height: 120%;\n  }\r\n  .lg\\:bold-52{\n    font-size: 52px;\n    font-weight: 700;\n    line-height: 120%;\n  }\n}\r\n.hover\\:scale-105:hover{\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:bg-primary-50:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(179 235 242 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-primary-600:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 168 183 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-white\\/20:hover{\n  background-color: rgb(255 255 255 / 0.2);\n}\r\n.hover\\:font-bold:hover{\n  font-weight: 700;\n}\r\n.hover\\:text-white:hover{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:opacity-70:hover{\n  opacity: 0.7;\n}\r\n.hover\\:opacity-80:hover{\n  opacity: 0.8;\n}\r\n.hover\\:shadow-lg:hover{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-xl:hover{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus\\:border-primary-400:focus{\n  --tw-border-opacity: 1;\n  border-color: rgb(57 201 216 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:outline-none:focus{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.group:hover .group-hover\\:text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n@media (min-width: 640px){\r\n\r\n  .sm\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\r\n\r\n  .sm\\:flex-row{\n    flex-direction: row;\n  }\n}\r\n@media (min-width: 768px){\r\n\r\n  .md\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\r\n\r\n  .md\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\r\n\r\n  .md\\:flex-row{\n    flex-direction: row;\n  }\r\n\r\n  .md\\:gap-16{\n    gap: 4rem;\n  }\r\n\r\n  .md\\:p-12{\n    padding: 3rem;\n  }\n}\r\n@media (min-width: 1024px){\r\n\r\n  .lg\\:flex{\n    display: flex;\n  }\r\n\r\n  .lg\\:hidden{\n    display: none;\n  }\r\n\r\n  .lg\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\r\n\r\n  .lg\\:py-32{\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\n}\r\n@media (min-width: 1280px){\r\n\r\n  .xl\\:w-1\\/2{\n    width: 50%;\n  }\r\n\r\n  .xl\\:max-w-\\[520px\\]{\n    max-width: 520px;\n  }\r\n\r\n  .xl\\:flex-row{\n    flex-direction: row;\n  }\n}\r\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAkBA;;;;;AAWA;;;;;;AAUA;;;;;AASA;;;;;AAcA;;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAAA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAYA;;;;AAQA;;;;AASA;;;;;AAAA;;;;;AAKA;;;;;AAUA;;;;AASA;;;;AAUA;;;;;AAgBA;;;;;AAQA;;;;AAGA;;;;;;;;;;;;;;AAgBA;;;;;AAIA;;;;;;;;;;;AAaA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;EAEE;;;;;;AAKF;EAEE;;;;;;AAKF;;;;;;AAKA;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAIA;;;;;;AAMA;;;;AAKA;;;;AACA;;;;;AACA;EACE;;;;;;EAKA;;;;;;;AAMF;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;EAEE;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA", "debugId": null}}]}