#!/usr/bin/env python3
"""
Simple Waterborne Disease Prediction Model for Northeast India
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import classification_report, accuracy_score, roc_auc_score
import joblib
import json

# Set random seeds for reproducibility
np.random.seed(42)

print('✅ Libraries imported successfully!')
print('📊 Starting Waterborne Disease Prediction modeling')

# Load the datasets
print('\n📁 Loading Northeast India datasets...')

# Load disease outbreak data
disease_df = pd.read_csv('northeast_states_disease_outbreaks.csv')
print(f'Disease outbreak data: {disease_df.shape}')

# Load water quality data
water_df = pd.read_csv('northeast_water_quality_data.csv')
print(f'Water quality data: {water_df.shape}')

# Data Preprocessing
print('\n🔧 Data Preprocessing...')

# Process disease data
disease_df['Date_of_Start_of_Outbreak'] = pd.to_datetime(disease_df['Date_of_Start_of_Outbreak'], format='%d-%m-%y', errors='coerce')
disease_df['outbreak_year'] = disease_df['Date_of_Start_of_Outbreak'].dt.year

# Categorize disease types
waterborne_diseases = ['Acute Diarrheal Disease', 'Cholera', 'Hepatitis A', 'Acute Diarrheal Diseases', 'Acute Diarrhoeal Disease']
disease_df['is_waterborne'] = disease_df['Disease_Illness'].isin(waterborne_diseases).astype(int)

print(f'Waterborne disease outbreaks: {disease_df["is_waterborne"].sum()} out of {len(disease_df)}')

# Process water quality data
# Fill missing values
water_df['fecal_streptococci'] = water_df['fecal_streptococci'].fillna(water_df['fecal_streptococci'].median())

# Create water quality risk indicators
water_df['ph_risk'] = ((water_df['ph'] < 6.5) | (water_df['ph'] > 8.5)).astype(int)
water_df['do_risk'] = (water_df['dissolved_oxygen'] < 5).astype(int)
water_df['bod_risk'] = (water_df['bod'] > 5).astype(int)
water_df['nitrate_risk'] = (water_df['nitrate_n'] > 10).astype(int)
water_df['fecal_coliform_risk'] = (water_df['fecal_coliform'] > 0).astype(int)
water_df['total_coliform_risk'] = (water_df['total_coliform'] > 0).astype(int)

# Create composite water quality index
risk_columns = ['ph_risk', 'do_risk', 'bod_risk', 'nitrate_risk', 'fecal_coliform_risk', 'total_coliform_risk']
water_df['water_quality_risk_score'] = water_df[risk_columns].sum(axis=1)

print(f'Water quality risk scores: min={water_df["water_quality_risk_score"].min()}, max={water_df["water_quality_risk_score"].max()}')

# Create training dataset by state aggregation
print('\n📊 Creating Training Dataset...')

# Aggregate disease data by state
disease_by_state = disease_df.groupby('Northeast_State').agg({
    'is_waterborne': 'sum',
    'No_of_Cases': 'sum',
    'No_of_Deaths': 'sum'
}).reset_index()

disease_by_state['has_waterborne_outbreak'] = (disease_by_state['is_waterborne'] > 0).astype(int)
disease_by_state['outbreak_rate'] = disease_by_state['is_waterborne'] / (disease_by_state['is_waterborne'] + (disease_df.groupby('Northeast_State').size() - disease_by_state['is_waterborne']))

# Aggregate water quality data by state
water_by_state = water_df.groupby('northeast_state').agg({
    'temperature': 'mean',
    'ph': 'mean',
    'dissolved_oxygen': 'mean',
    'bod': 'mean',
    'nitrate_n': 'mean',
    'fecal_coliform': 'mean',
    'total_coliform': 'mean',
    'fecal_streptococci': 'mean',
    'water_quality_risk_score': 'mean'
}).reset_index()

# Merge datasets
merged_df = pd.merge(disease_by_state, water_by_state, 
                    left_on='Northeast_State', right_on='northeast_state', how='inner')

print(f'Merged dataset shape: {merged_df.shape}')
print(f'States in merged data: {merged_df["Northeast_State"].tolist()}')

# Prepare features and target
feature_columns = ['temperature', 'ph', 'dissolved_oxygen', 'bod', 'nitrate_n', 
                  'fecal_coliform', 'total_coliform', 'fecal_streptococci', 'water_quality_risk_score']

# Handle any remaining missing values
X = merged_df[feature_columns].fillna(merged_df[feature_columns].median())
y = merged_df['has_waterborne_outbreak']

print(f'\nFeature matrix shape: {X.shape}')
print(f'Target distribution: {y.value_counts().to_dict()}')

# Check for any remaining NaN values
print(f'Any NaN in features: {X.isnull().any().any()}')
print(f'Any NaN in target: {y.isnull().any()}')

if X.isnull().any().any():
    print('Filling remaining NaN values...')
    X = X.fillna(0)

print('\n🤖 Training Machine Learning Models...')

# Since we have a very small dataset, we'll use simple models and cross-validation
models = {
    'Random Forest': RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced'),
    'Logistic Regression': LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000),
    'Naive Bayes': GaussianNB()
}

# Scale features for models that need it
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

results = {}

print('Model Performance (Cross-Validation):')
print('=' * 50)

for name, model in models.items():
    if name in ['Logistic Regression', 'Naive Bayes']:
        # Use scaled features
        cv_scores = cross_val_score(model, X_scaled, y, cv=3, scoring='accuracy')  # Use cv=3 due to small dataset
        model.fit(X_scaled, y)
        y_pred_proba = model.predict_proba(X_scaled)[:, 1] if hasattr(model, 'predict_proba') else None
    else:
        # Use original features for tree-based models
        cv_scores = cross_val_score(model, X, y, cv=3, scoring='accuracy')
        model.fit(X, y)
        y_pred_proba = model.predict_proba(X)[:, 1] if hasattr(model, 'predict_proba') else None
    
    # Calculate AUC if possible
    try:
        auc_score = roc_auc_score(y, y_pred_proba) if y_pred_proba is not None else 0.5
    except:
        auc_score = 0.5
    
    results[name] = {
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'auc': auc_score,
        'model': model
    }
    
    print(f'{name:20} | CV Score: {cv_scores.mean():.3f} ± {cv_scores.std():.3f} | AUC: {auc_score:.3f}')

# Find best model
best_model_name = max(results.keys(), key=lambda x: results[x]['cv_mean'])
best_model = results[best_model_name]['model']

print(f'\n🏆 Best Model: {best_model_name}')
print(f'Cross-validation Score: {results[best_model_name]["cv_mean"]:.3f}')
print(f'AUC Score: {results[best_model_name]["auc"]:.3f}')

# Feature importance for Random Forest
if 'Random Forest' in results:
    rf_model = results['Random Forest']['model']
    feature_importance = pd.DataFrame({
        'feature': feature_columns,
        'importance': rf_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print('\n🔍 Feature Importance (Random Forest):')
    print('=' * 40)
    for _, row in feature_importance.iterrows():
        print(f'{row["feature"]:25} | {row["importance"]:.4f}')

# Create prediction function
def predict_waterborne_disease_risk(temperature, ph, dissolved_oxygen, bod, nitrate_n, 
                                   fecal_coliform, total_coliform, fecal_streptococci):
    """
    Predict waterborne disease outbreak risk based on water quality parameters.
    """
    
    # Create water quality risk indicators
    ph_risk = 1 if (ph < 6.5 or ph > 8.5) else 0
    do_risk = 1 if dissolved_oxygen < 5 else 0
    bod_risk = 1 if bod > 5 else 0
    nitrate_risk = 1 if nitrate_n > 10 else 0
    fecal_coliform_risk = 1 if fecal_coliform > 0 else 0
    total_coliform_risk = 1 if total_coliform > 0 else 0
    
    water_quality_risk_score = (ph_risk + do_risk + bod_risk + nitrate_risk + 
                               fecal_coliform_risk + total_coliform_risk)
    
    # Prepare input features
    input_features = np.array([[temperature, ph, dissolved_oxygen, bod, nitrate_n,
                               fecal_coliform, total_coliform, fecal_streptococci,
                               water_quality_risk_score]])
    
    # Make prediction
    if best_model_name in ['Logistic Regression', 'Naive Bayes']:
        input_scaled = scaler.transform(input_features)
        prediction = best_model.predict(input_scaled)[0]
        probability = best_model.predict_proba(input_scaled)[0][1] if hasattr(best_model, 'predict_proba') else 0.5
    else:
        prediction = best_model.predict(input_features)[0]
        probability = best_model.predict_proba(input_features)[0][1] if hasattr(best_model, 'predict_proba') else 0.5
    
    # Determine risk level
    if probability < 0.3:
        risk_level = "Low"
    elif probability < 0.7:
        risk_level = "Medium"
    else:
        risk_level = "High"
    
    # Generate recommendations
    recommendations = []
    
    if ph_risk:
        recommendations.append("pH levels are outside safe range (6.5-8.5). Consider water treatment.")
    if do_risk:
        recommendations.append("Low dissolved oxygen detected. Check for organic pollution.")
    if bod_risk:
        recommendations.append("High BOD indicates organic contamination. Improve wastewater treatment.")
    if nitrate_risk:
        recommendations.append("High nitrate levels detected. Check for agricultural runoff.")
    if fecal_coliform_risk:
        recommendations.append("Fecal coliform detected. Implement disinfection measures immediately.")
    if total_coliform_risk:
        recommendations.append("Total coliform detected. Monitor water source and treatment systems.")
    
    if not recommendations:
        recommendations.append("Water quality parameters are within acceptable ranges. Continue regular monitoring.")
    
    if risk_level == "High":
        recommendations.append("HIGH RISK: Implement immediate public health measures and water advisories.")
    elif risk_level == "Medium":
        recommendations.append("MEDIUM RISK: Increase monitoring frequency and prepare preventive measures.")
    
    return {
        'prediction': int(prediction),
        'probability': float(probability),
        'risk_level': risk_level,
        'water_quality_risk_score': int(water_quality_risk_score),
        'recommendations': recommendations,
        'model_used': best_model_name,
        'prediction_timestamp': datetime.now().isoformat()
    }

# Test the prediction function
print('\n🧪 Testing Prediction Function:')

# Test case 1: High risk scenario
test_high_risk = predict_waterborne_disease_risk(
    temperature=25, ph=4.5, dissolved_oxygen=3, bod=15, nitrate_n=15,
    fecal_coliform=200, total_coliform=1000, fecal_streptococci=50
)

print('\nHigh Risk Test Case:')
print(f'Risk Level: {test_high_risk["risk_level"]} (Probability: {test_high_risk["probability"]:.3f})')
print(f'Recommendations: {len(test_high_risk["recommendations"])} items')

# Test case 2: Low risk scenario
test_low_risk = predict_waterborne_disease_risk(
    temperature=20, ph=7.2, dissolved_oxygen=8, bod=2, nitrate_n=3,
    fecal_coliform=0, total_coliform=0, fecal_streptococci=0
)

print('\nLow Risk Test Case:')
print(f'Risk Level: {test_low_risk["risk_level"]} (Probability: {test_low_risk["probability"]:.3f})')
print(f'Recommendations: {len(test_low_risk["recommendations"])} items')

# Save the model and scaler for deployment
print('\n💾 Saving Model for Deployment:')

# Save the scaler
joblib.dump(scaler, 'water_quality_scaler.pkl')
print('✅ Scaler saved as "water_quality_scaler.pkl"')

# Save the best model
joblib.dump(best_model, 'waterborne_disease_model.pkl')
print('✅ Model saved as "waterborne_disease_model.pkl"')

# Save model metadata
model_metadata = {
    'model_name': best_model_name,
    'model_type': 'traditional',
    'cv_score': float(results[best_model_name]['cv_mean']),
    'auc': float(results[best_model_name]['auc']),
    'feature_columns': feature_columns,
    'training_date': datetime.now().isoformat(),
    'training_samples': len(merged_df),
    'version': '1.0'
}

with open('model_metadata.json', 'w') as f:
    json.dump(model_metadata, f, indent=2)

print('✅ Model metadata saved as "model_metadata.json"')

print('\n🎉 Model Development Complete!')
print('\n📋 Summary:')
print(f'Dataset: {len(merged_df)} samples from {len(merged_df)} Northeast Indian states')
print(f'Best Model: {best_model_name}')
print(f'Cross-validation Score: {results[best_model_name]["cv_mean"]:.3f}')
print(f'AUC Score: {results[best_model_name]["auc"]:.3f}')
print('\n📁 Files created:')
print('- waterborne_disease_model.pkl (trained model)')
print('- water_quality_scaler.pkl (feature scaler)')
print('- model_metadata.json (model information)')
