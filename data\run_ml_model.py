#!/usr/bin/env python3
"""
Waterborne Disease Prediction Model for Northeast India
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, roc_auc_score, roc_curve
from sklearn.feature_selection import SelectKBest, f_classif
import joblib
import json

# Set random seeds for reproducibility
np.random.seed(42)

print('✅ Libraries imported successfully!')
print('📊 Ready for Waterborne Disease Prediction modeling')

# Load the datasets
print('\n📁 Loading Northeast India datasets...')

# Load disease outbreak data
disease_df = pd.read_csv('northeast_states_disease_outbreaks.csv')
print(f'Disease outbreak data: {disease_df.shape}')

# Load water quality data
water_df = pd.read_csv('northeast_water_quality_data.csv')
print(f'Water quality data: {water_df.shape}')

print('\n🔍 Disease Outbreak Data Overview:')
print(disease_df.head())
print('\nColumns:', disease_df.columns.tolist())

print('\n🔍 Water Quality Data Overview:')
print(water_df.head())
print('\nColumns:', water_df.columns.tolist())

# Basic data exploration
print('\n📊 Disease Data Analysis:')
print(f'Total outbreaks: {len(disease_df)}')
print(f'States covered: {disease_df["Northeast_State"].nunique()}')
print(f'Disease types:')
print(disease_df['Disease_Illness'].value_counts())
print(f'\nOutbreaks by state:')
print(disease_df['Northeast_State'].value_counts())

print('\n🌊 Water Quality Data Analysis:')
print(f'Total water samples: {len(water_df)}')
print(f'States with water data: {water_df["northeast_state"].nunique()}')
print(f'Water quality by state:')
print(water_df['northeast_state'].value_counts())

# Check for missing values
print('\n❓ Missing Values:')
print('Disease data missing values:')
print(disease_df.isnull().sum())
print('\nWater quality missing values:')
print(water_df.isnull().sum())

# Data Preprocessing and Feature Engineering
print('\n🔧 Starting Data Preprocessing...')

print('\n1️⃣ Processing Disease Data:')

# Convert date columns
disease_df['Date_of_Start_of_Outbreak'] = pd.to_datetime(disease_df['Date_of_Start_of_Outbreak'], format='%d-%m-%y', errors='coerce')
disease_df['Date_of_Reporting'] = pd.to_datetime(disease_df['Date_of_Reporting'], format='%d-%m-%y', errors='coerce')

# Extract year and month for temporal analysis
disease_df['outbreak_year'] = disease_df['Date_of_Start_of_Outbreak'].dt.year
disease_df['outbreak_month'] = disease_df['Date_of_Start_of_Outbreak'].dt.month

# Create severity indicators
disease_df['cases_per_death'] = disease_df['No_of_Cases'] / (disease_df['No_of_Deaths'] + 1)  # +1 to avoid division by zero
disease_df['mortality_rate'] = disease_df['No_of_Deaths'] / disease_df['No_of_Cases']
disease_df['severity_score'] = disease_df['No_of_Cases'] + (disease_df['No_of_Deaths'] * 10)  # Weight deaths more heavily

# Categorize disease types
waterborne_diseases = ['Acute Diarrheal Disease', 'Cholera', 'Hepatitis A']
disease_df['is_waterborne'] = disease_df['Disease_Illness'].isin(waterborne_diseases).astype(int)

# Create outbreak intensity categories
disease_df['outbreak_intensity'] = pd.cut(disease_df['No_of_Cases'], 
                                        bins=[0, 10, 25, 50, float('inf')], 
                                        labels=['Low', 'Medium', 'High', 'Very High'])

print(f'Waterborne disease outbreaks: {disease_df["is_waterborne"].sum()}')
print(f'Total outbreaks: {len(disease_df)}')
print(f'Outbreak intensity distribution:')
print(disease_df['outbreak_intensity'].value_counts())

print('\n2️⃣ Processing Water Quality Data:')

# Handle missing values in water quality data
water_df['fecal_streptococci'] = water_df['fecal_streptococci'].fillna(water_df['fecal_streptococci'].median())

# Create water quality risk indicators
# WHO/EPA standards for drinking water
water_df['ph_risk'] = ((water_df['ph'] < 6.5) | (water_df['ph'] > 8.5)).astype(int)
water_df['do_risk'] = (water_df['dissolved_oxygen'] < 5).astype(int)  # Low DO indicates pollution
water_df['bod_risk'] = (water_df['bod'] > 5).astype(int)  # High BOD indicates organic pollution
water_df['nitrate_risk'] = (water_df['nitrate_n'] > 10).astype(int)  # WHO guideline
water_df['fecal_coliform_risk'] = (water_df['fecal_coliform'] > 0).astype(int)  # Any presence is risky
water_df['total_coliform_risk'] = (water_df['total_coliform'] > 0).astype(int)

# Create composite water quality index
risk_columns = ['ph_risk', 'do_risk', 'bod_risk', 'nitrate_risk', 'fecal_coliform_risk', 'total_coliform_risk']
water_df['water_quality_risk_score'] = water_df[risk_columns].sum(axis=1)
water_df['water_quality_category'] = pd.cut(water_df['water_quality_risk_score'], 
                                          bins=[-1, 1, 3, 5, 6], 
                                          labels=['Good', 'Fair', 'Poor', 'Very Poor'])

print(f'Water quality categories:')
print(water_df['water_quality_category'].value_counts())
print(f'\nWater quality risk scores:')
print(water_df['water_quality_risk_score'].describe())

print('\n3️⃣ Creating Training Dataset:')

# Aggregate disease data by state and year to match with water quality data
disease_summary = disease_df.groupby(['Northeast_State', 'outbreak_year']).agg({
    'is_waterborne': 'sum',
    'No_of_Cases': 'sum',
    'No_of_Deaths': 'sum',
    'severity_score': 'sum',
    'outbreak_intensity': lambda x: (x == 'High').sum() + (x == 'Very High').sum()  # Count high-intensity outbreaks
}).reset_index()

disease_summary.columns = ['state', 'year', 'waterborne_outbreaks', 'total_cases', 'total_deaths', 'total_severity', 'high_intensity_outbreaks']

# Create binary target: 1 if there were waterborne disease outbreaks, 0 otherwise
disease_summary['has_waterborne_outbreak'] = (disease_summary['waterborne_outbreaks'] > 0).astype(int)

# Create risk level target (multi-class)
disease_summary['risk_level'] = pd.cut(disease_summary['waterborne_outbreaks'], 
                                     bins=[-1, 0, 2, 5, float('inf')], 
                                     labels=[0, 1, 2, 3])  # 0=No Risk, 1=Low, 2=Medium, 3=High

print(f'Disease summary shape: {disease_summary.shape}')
print(f'\nTarget distribution (binary):')
print(disease_summary['has_waterborne_outbreak'].value_counts())
print(f'\nTarget distribution (multi-class):')
print(disease_summary['risk_level'].value_counts())

# Prepare water quality data for merging
water_summary = water_df.groupby(['northeast_state', 'sample_date']).agg({
    'temperature': 'mean',
    'ph': 'mean',
    'dissolved_oxygen': 'mean',
    'bod': 'mean',
    'nitrate_n': 'mean',
    'fecal_coliform': 'mean',
    'total_coliform': 'mean',
    'fecal_streptococci': 'mean',
    'water_quality_risk_score': 'mean'
}).reset_index()

water_summary.columns = ['state', 'year', 'temperature', 'ph', 'dissolved_oxygen', 'bod', 
                        'nitrate_n', 'fecal_coliform', 'total_coliform', 'fecal_streptococci', 'water_quality_risk_score']

print(f'\nWater summary shape: {water_summary.shape}')

# Merge datasets
# Since we have limited water quality data, we'll use a strategy to create more training samples
# We'll assume water quality conditions are relatively stable within a state over nearby years

merged_data = []

for _, disease_row in disease_summary.iterrows():
    state = disease_row['state']
    year = disease_row['year']
    
    # Find water quality data for the same state and closest year
    state_water = water_summary[water_summary['state'] == state]
    
    if len(state_water) > 0:
        # Find closest year
        closest_water = state_water.iloc[(state_water['year'] - year).abs().argsort()[:1]]
        
        if len(closest_water) > 0:
            combined_row = {**disease_row.to_dict(), **closest_water.iloc[0].to_dict()}
            # Remove duplicate columns
            combined_row['state'] = state
            combined_row['year'] = year
            merged_data.append(combined_row)

# Create final training dataset
training_df = pd.DataFrame(merged_data)
print(f'\n✅ Final training dataset shape: {training_df.shape}')
print(f'States in training data: {training_df["state"].unique()}')
print(f'Years covered: {sorted(training_df["year"].unique())}')

print('\n🤖 Building Traditional ML Models...')

# Prepare features and target
feature_columns = ['temperature', 'ph', 'dissolved_oxygen', 'bod', 'nitrate_n', 
                  'fecal_coliform', 'total_coliform', 'fecal_streptococci', 'water_quality_risk_score']

# Handle any remaining missing values
X = training_df[feature_columns].fillna(training_df[feature_columns].median())
y_binary = training_df['has_waterborne_outbreak']
y_multiclass = training_df['risk_level'].astype(int)

print(f'Feature matrix shape: {X.shape}')
print(f'Binary target distribution: {y_binary.value_counts().to_dict()}')
print(f'Multi-class target distribution: {y_multiclass.value_counts().to_dict()}')

# Split the data
X_train, X_test, y_train_bin, y_test_bin = train_test_split(X, y_binary, test_size=0.3, random_state=42, stratify=y_binary)
_, _, y_train_multi, y_test_multi = train_test_split(X, y_multiclass, test_size=0.3, random_state=42, stratify=y_multiclass)

# Scale the features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print(f'\nTraining set size: {X_train.shape[0]}')
print(f'Test set size: {X_test.shape[0]}')

# Define models for binary classification
models_binary = {
    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
    'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42),
    'Logistic Regression': LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000),
    'SVM': SVC(random_state=42, class_weight='balanced', probability=True),
    'Naive Bayes': GaussianNB(),
    'Decision Tree': DecisionTreeClassifier(random_state=42, class_weight='balanced')
}

# Train and evaluate binary classification models
print('\n🎯 Binary Classification Results (Outbreak vs No Outbreak):')
print('=' * 80)

binary_results = {}

for name, model in models_binary.items():
    # Use scaled features for SVM and Logistic Regression, original for tree-based models
    if name in ['SVM', 'Logistic Regression', 'Naive Bayes']:
        model.fit(X_train_scaled, y_train_bin)
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
    else:
        model.fit(X_train, y_train_bin)
        y_pred = model.predict(X_test)
        y_pred_proba = model.predict_proba(X_test)[:, 1]
    
    # Calculate metrics
    accuracy = accuracy_score(y_test_bin, y_pred)
    try:
        auc_score = roc_auc_score(y_test_bin, y_pred_proba)
    except:
        auc_score = 0.5
    
    # Cross-validation score
    if name in ['SVM', 'Logistic Regression', 'Naive Bayes']:
        cv_scores = cross_val_score(model, X_train_scaled, y_train_bin, cv=5, scoring='accuracy')
    else:
        cv_scores = cross_val_score(model, X_train, y_train_bin, cv=5, scoring='accuracy')
    
    binary_results[name] = {
        'accuracy': accuracy,
        'auc': auc_score,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'model': model
    }
    
    print(f'{name:20} | Accuracy: {accuracy:.3f} | AUC: {auc_score:.3f} | CV: {cv_scores.mean():.3f} ± {cv_scores.std():.3f}')

# Find best binary model
best_binary_model = max(binary_results.items(), key=lambda x: x[1]['cv_mean'])
print(f'\n🏆 Best Binary Model: {best_binary_model[0]} (CV Score: {best_binary_model[1]["cv_mean"]:.3f})')

# Feature Importance Analysis
print('\n🔍 Feature Importance Analysis:')
print('=' * 50)

# Get feature importance from the best tree-based model
tree_models = ['Random Forest', 'Gradient Boosting', 'Decision Tree']
best_tree_model = None
best_tree_score = 0

for model_name in tree_models:
    if model_name in binary_results and binary_results[model_name]['cv_mean'] > best_tree_score:
        best_tree_score = binary_results[model_name]['cv_mean']
        best_tree_model = binary_results[model_name]['model']
        best_tree_name = model_name

if best_tree_model is not None:
    feature_importance = pd.DataFrame({
        'feature': feature_columns,
        'importance': best_tree_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f'Feature Importance from {best_tree_name}:')
    for _, row in feature_importance.iterrows():
        print(f'{row["feature"]:25} | {row["importance"]:.4f}')

# Detailed classification report for best binary model
print('\n📊 Detailed Classification Report (Best Binary Model):')
print('=' * 60)

best_model = best_binary_model[1]['model']
if best_binary_model[0] in ['SVM', 'Logistic Regression', 'Naive Bayes']:
    y_pred_best = best_model.predict(X_test_scaled)
else:
    y_pred_best = best_model.predict(X_test)

print(classification_report(y_test_bin, y_pred_best, target_names=['No Outbreak', 'Outbreak']))

# Save the model and scaler for deployment
print('\n💾 Saving Model for Deployment:')

# Save the scaler
joblib.dump(scaler, 'water_quality_scaler.pkl')
print('✅ Scaler saved as "water_quality_scaler.pkl"')

# Save the best model
joblib.dump(best_model, 'waterborne_disease_model.pkl')
print('✅ Model saved as "waterborne_disease_model.pkl"')

# Save model metadata
model_metadata = {
    'model_name': best_binary_model[0],
    'model_type': 'traditional',
    'accuracy': float(best_binary_model[1]['accuracy']),
    'auc': float(best_binary_model[1]['auc']),
    'feature_columns': feature_columns,
    'training_date': datetime.now().isoformat(),
    'training_samples': len(training_df),
    'version': '1.0'
}

with open('model_metadata.json', 'w') as f:
    json.dump(model_metadata, f, indent=2)

print('✅ Model metadata saved as "model_metadata.json"')

print('\n🎉 Model Development Complete!')
print('\n📋 Summary:')
print(f'Best Model: {best_binary_model[0]}')
print(f'Accuracy: {best_binary_model[1]["accuracy"]:.3f}')
print(f'AUC: {best_binary_model[1]["auc"]:.3f}')
print(f'Cross-validation Score: {best_binary_model[1]["cv_mean"]:.3f}')
