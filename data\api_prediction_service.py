#!/usr/bin/env python3
"""
API-Ready Prediction Service for Waterborne Disease Risk
Simple interface for web applications and APIs
"""

from optimized_waterborne_model import WaterborneDiseasePredictionModel
import json
from datetime import datetime

class WaterborneRiskAPI:
    """
    Simple API wrapper for waterborne disease risk prediction
    """
    
    def __init__(self, model_path="./"):
        """Initialize the API with a trained model"""
        self.model = WaterborneDiseasePredictionModel(model_path)
        try:
            self.model.load_model('optimized_waterborne_model')
            print("✅ API initialized with trained model")
        except FileNotFoundError:
            print("❌ No trained model found. Please train a model first.")
            raise
    
    def predict_risk(self, water_quality_params):
        """
        Predict waterborne disease risk from water quality parameters
        
        Args:
            water_quality_params (dict): Water quality measurements
                Required keys:
                - temperature (float): Water temperature in °C
                - ph (float): pH level (0-14)
                - dissolved_oxygen (float): Dissolved oxygen in mg/L
                - bod (float): Biochemical Oxygen Demand in mg/L
                - nitrate_n (float): Nitrate-N concentration in mg/L
                - fecal_coliform (float): Fecal coliform count (CFU/100ml)
                - total_coliform (float): Total coliform count (CFU/100ml)
                - fecal_streptococci (float): Fecal streptococci count (CFU/100ml)
                - water_quality_risk_score (int): Composite risk score (0-6)
        
        Returns:
            dict: Prediction results with risk assessment and recommendations
        """
        
        try:
            # Make prediction
            result = self.model.predict(water_quality_params)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(water_quality_params, result)
            
            # Format response
            response = {
                'status': 'success',
                'prediction': {
                    'risk_level': result['risk_level'],
                    'probability': round(result['probability'], 3),
                    'outbreak_risk': result['prediction'] == 1,
                    'confidence': 'High' if result['probability'] > 0.8 or result['probability'] < 0.2 else 'Medium'
                },
                'water_quality_assessment': self._assess_water_quality(water_quality_params),
                'recommendations': recommendations,
                'metadata': {
                    'prediction_timestamp': datetime.now().isoformat(),
                    'model_version': self.model.metadata.get('version', 'Unknown'),
                    'model_accuracy': f"{self.model.metadata.get('cv_accuracy_mean', 0):.1%}"
                }
            }
            
            return response
            
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _assess_water_quality(self, params):
        """Assess individual water quality parameters"""
        assessment = {}
        
        # pH assessment
        if params['ph'] < 6.5:
            assessment['ph'] = {'status': 'Poor', 'issue': 'Too acidic'}
        elif params['ph'] > 8.5:
            assessment['ph'] = {'status': 'Poor', 'issue': 'Too alkaline'}
        else:
            assessment['ph'] = {'status': 'Good', 'issue': None}
        
        # Dissolved oxygen assessment
        if params['dissolved_oxygen'] < 5:
            assessment['dissolved_oxygen'] = {'status': 'Poor', 'issue': 'Low oxygen levels'}
        elif params['dissolved_oxygen'] > 8:
            assessment['dissolved_oxygen'] = {'status': 'Excellent', 'issue': None}
        else:
            assessment['dissolved_oxygen'] = {'status': 'Good', 'issue': None}
        
        # BOD assessment
        if params['bod'] > 5:
            assessment['bod'] = {'status': 'Poor', 'issue': 'High organic pollution'}
        else:
            assessment['bod'] = {'status': 'Good', 'issue': None}
        
        # Nitrate assessment
        if params['nitrate_n'] > 10:
            assessment['nitrate_n'] = {'status': 'Poor', 'issue': 'High nitrate levels'}
        else:
            assessment['nitrate_n'] = {'status': 'Good', 'issue': None}
        
        # Bacterial contamination assessment
        if params['fecal_coliform'] > 0:
            assessment['bacterial_contamination'] = {'status': 'Poor', 'issue': 'Fecal contamination detected'}
        elif params['total_coliform'] > 100:
            assessment['bacterial_contamination'] = {'status': 'Fair', 'issue': 'High coliform levels'}
        else:
            assessment['bacterial_contamination'] = {'status': 'Good', 'issue': None}
        
        return assessment
    
    def _generate_recommendations(self, params, prediction):
        """Generate specific recommendations based on water quality and prediction"""
        recommendations = []
        
        # Risk-based recommendations
        if prediction['risk_level'] == 'High':
            recommendations.append("🚨 IMMEDIATE ACTION REQUIRED: High risk of waterborne disease outbreak")
            recommendations.append("Implement emergency water treatment measures")
            recommendations.append("Issue public health advisory")
            recommendations.append("Increase disease surveillance")
        elif prediction['risk_level'] == 'Medium':
            recommendations.append("⚠️ CAUTION: Moderate risk detected")
            recommendations.append("Increase water quality monitoring frequency")
            recommendations.append("Prepare preventive measures")
        else:
            recommendations.append("✅ LOW RISK: Water quality within acceptable ranges")
            recommendations.append("Continue regular monitoring")
        
        # Parameter-specific recommendations
        if params['ph'] < 6.5 or params['ph'] > 8.5:
            recommendations.append("Adjust pH levels to 6.5-8.5 range through water treatment")
        
        if params['dissolved_oxygen'] < 5:
            recommendations.append("Investigate sources of organic pollution affecting oxygen levels")
        
        if params['bod'] > 5:
            recommendations.append("Improve wastewater treatment to reduce organic contamination")
        
        if params['nitrate_n'] > 10:
            recommendations.append("Check for agricultural runoff and implement nutrient management")
        
        if params['fecal_coliform'] > 0:
            recommendations.append("Implement immediate disinfection measures")
            recommendations.append("Identify and eliminate sources of fecal contamination")
        
        if params['total_coliform'] > 100:
            recommendations.append("Enhance water treatment and distribution system maintenance")
        
        return recommendations
    
    def batch_predict(self, water_samples):
        """
        Predict risk for multiple water samples
        
        Args:
            water_samples (list): List of water quality parameter dictionaries
        
        Returns:
            dict: Batch prediction results
        """
        try:
            results = self.model.predict(water_samples)
            
            batch_response = {
                'status': 'success',
                'total_samples': len(water_samples),
                'predictions': [],
                'summary': {
                    'high_risk_count': 0,
                    'medium_risk_count': 0,
                    'low_risk_count': 0
                },
                'metadata': {
                    'prediction_timestamp': datetime.now().isoformat(),
                    'model_version': self.model.metadata.get('version', 'Unknown')
                }
            }
            
            for i, (sample, pred, prob, risk) in enumerate(zip(
                water_samples, results['predictions'], results['probabilities'], results['risk_levels']
            )):
                sample_result = {
                    'sample_id': i + 1,
                    'risk_level': risk,
                    'probability': round(prob, 3),
                    'outbreak_risk': pred == 1,
                    'water_quality_assessment': self._assess_water_quality(sample)
                }
                
                batch_response['predictions'].append(sample_result)
                
                # Update summary
                if risk == 'High':
                    batch_response['summary']['high_risk_count'] += 1
                elif risk == 'Medium':
                    batch_response['summary']['medium_risk_count'] += 1
                else:
                    batch_response['summary']['low_risk_count'] += 1
            
            return batch_response
            
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_model_info(self):
        """Get information about the loaded model"""
        return {
            'model_name': self.model.metadata.get('model_name', 'Unknown'),
            'training_date': self.model.metadata.get('training_date', 'Unknown'),
            'accuracy': f"{self.model.metadata.get('cv_accuracy_mean', 0):.1%}",
            'auc_score': self.model.metadata.get('auc_score', 0),
            'total_training_samples': self.model.metadata.get('total_samples', 0),
            'features_count': len(self.model.metadata.get('feature_columns', [])),
            'version': self.model.metadata.get('version', 'Unknown')
        }

# Example usage and testing
def test_api():
    """Test the API with sample data"""
    print("🧪 Testing Waterborne Risk API")
    print("=" * 40)
    
    # Initialize API
    api = WaterborneRiskAPI()
    
    # Test single prediction
    print("\n1. Single Prediction Test:")
    test_sample = {
        'temperature': 25,
        'ph': 4.5,
        'dissolved_oxygen': 3,
        'bod': 15,
        'nitrate_n': 15,
        'fecal_coliform': 200,
        'total_coliform': 1000,
        'fecal_streptococci': 50,
        'water_quality_risk_score': 6
    }
    
    result = api.predict_risk(test_sample)
    print(f"Status: {result['status']}")
    print(f"Risk Level: {result['prediction']['risk_level']}")
    print(f"Probability: {result['prediction']['probability']}")
    print(f"Recommendations: {len(result['recommendations'])} items")
    
    # Test batch prediction
    print("\n2. Batch Prediction Test:")
    batch_samples = [
        {  # Clean water
            'temperature': 20, 'ph': 7.2, 'dissolved_oxygen': 8, 'bod': 2,
            'nitrate_n': 1, 'fecal_coliform': 0, 'total_coliform': 5,
            'fecal_streptococci': 0, 'water_quality_risk_score': 0
        },
        {  # Contaminated water
            'temperature': 28, 'ph': 4.8, 'dissolved_oxygen': 2, 'bod': 20,
            'nitrate_n': 18, 'fecal_coliform': 300, 'total_coliform': 1500,
            'fecal_streptococci': 80, 'water_quality_risk_score': 6
        }
    ]
    
    batch_result = api.batch_predict(batch_samples)
    print(f"Status: {batch_result['status']}")
    print(f"Total Samples: {batch_result['total_samples']}")
    print(f"High Risk: {batch_result['summary']['high_risk_count']}")
    print(f"Medium Risk: {batch_result['summary']['medium_risk_count']}")
    print(f"Low Risk: {batch_result['summary']['low_risk_count']}")
    
    # Model info
    print("\n3. Model Information:")
    model_info = api.get_model_info()
    for key, value in model_info.items():
        print(f"{key}: {value}")
    
    print("\n✅ API testing completed!")

if __name__ == "__main__":
    test_api()
