# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
frontend/node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
frontend/coverage/

# next.js
/.next/
frontend/.next/
/out/
frontend/out/

# production
/build
frontend/build/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
frontend/.env*

# vercel
.vercel
frontend/.vercel/

# typescript
*.tsbuildinfo
next-env.d.ts
frontend/next-env.d.ts

# Data folders containing PDFs
data/pdf_files/
data/water\ data/

# PDF files anywhere in the project
*.pdf

# Jupyter Notebook checkpoints
.ipynb_checkpoints/
*/.ipynb_checkpoints/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Temporary files
*.tmp
*.temp
