{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/app/get-started/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport React, { useState } from 'react'\r\nimport Image from 'next/image'\r\nimport Link from 'next/link'\r\n\r\nconst GetStartedPage = () => {\r\n  const [formData, setFormData] = useState({\r\n    name: '',\r\n    symptoms: ''\r\n  })\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\r\n    const { name, value } = e.target\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }))\r\n  }\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault()\r\n    // Handle form submission here\r\n    console.log('Form submitted:', formData)\r\n    // You can add logic to process the form data\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 via-white to-primary-100\">\r\n      {/* Header */}\r\n      <header className=\"bg-white shadow-sm border-b-2 border-primary-200\">\r\n        <div className=\"w-full px-4 py-4\">\r\n          <Link href=\"/\" className=\"inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 hover:bg-primary-600 group\">\r\n            <span className=\"text-primary-600 font-semibold group-hover:text-white transition-colors duration-300\">← Back to Home</span>\r\n          </Link>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Main Content */}\r\n      <main className=\"max-container padding-container py-16\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          {/* Welcome Section */}\r\n          <div className=\"text-center mb-12\">\r\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-primary-100 rounded-full mb-6\">\r\n              <Image src=\"/user.svg\" alt=\"User\" width={32} height={32} className=\"text-primary-600\" />\r\n            </div>\r\n            <h1 className=\"bold-40 lg:bold-52 text-gray-90 mb-4\">\r\n              We're Here to Help You\r\n            </h1>\r\n            <p className=\"regular-18 text-gray-50 max-w-2xl mx-auto mb-2\">\r\n              Your health and well-being matter to us. Please share some basic information \r\n              so we can provide you with the most relevant guidance and support.\r\n            </p>\r\n            <p className=\"regular-16 text-primary-600 italic\">\r\n              Feel free to be as detailed or as brief as you're comfortable with. \r\n              Every piece of information helps us serve you better.\r\n            </p>\r\n          </div>\r\n\r\n          {/* Form Section */}\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-8 md:p-12\">\r\n            <form onSubmit={handleSubmit} className=\"space-y-8\">\r\n              {/* Name Field */}\r\n              <div>\r\n                <label htmlFor=\"name\" className=\"block bold-18 text-gray-90 mb-3\">\r\n                  What should we call you? \r\n                  <span className=\"regular-16 text-gray-50 font-normal ml-2\">(First name is perfectly fine)</span>\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"name\"\r\n                  name=\"name\"\r\n                  value={formData.name}\r\n                  onChange={handleInputChange}\r\n                  placeholder=\"Enter your name...\"\r\n                  className=\"w-full px-6 py-4 border-2 border-primary-200 rounded-xl focus:border-primary-400 focus:outline-none transition-colors regular-16 bg-gray-10\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              {/* Symptoms Field */}\r\n              <div>\r\n                <label htmlFor=\"symptoms\" className=\"block bold-18 text-gray-90 mb-3\">\r\n                  How are you feeling? \r\n                  <span className=\"regular-16 text-gray-50 font-normal ml-2\">(Describe any symptoms or concerns)</span>\r\n                </label>\r\n                <div className=\"mb-4\">\r\n                  <p className=\"regular-14 text-gray-50 mb-2\">You can mention things like:</p>\r\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-primary-600\">\r\n                    <span>• Stomach pain or discomfort</span>\r\n                    <span>• Nausea or vomiting</span>\r\n                    <span>• Diarrhea or digestive issues</span>\r\n                    <span>• Fever or chills</span>\r\n                    <span>• Fatigue or weakness</span>\r\n                    <span>• Headaches</span>\r\n                    <span>• Any other concerns</span>\r\n                    <span>• Recent water/food consumption</span>\r\n                  </div>\r\n                </div>\r\n                <textarea\r\n                  id=\"symptoms\"\r\n                  name=\"symptoms\"\r\n                  value={formData.symptoms}\r\n                  onChange={handleInputChange}\r\n                  placeholder=\"Take your time to describe how you're feeling. There's no rush, and you can be as detailed or brief as you'd like...\"\r\n                  rows={8}\r\n                  className=\"w-full px-6 py-4 border-2 border-primary-200 rounded-xl focus:border-primary-400 focus:outline-none transition-colors regular-16 bg-gray-10 resize-none\"\r\n                  required\r\n                />\r\n                <p className=\"regular-14 text-gray-50 mt-2 italic\">\r\n                  Remember: This information helps us provide better guidance, but it's not a substitute for professional medical advice.\r\n                </p>\r\n              </div>\r\n\r\n              {/* Submit Button */}\r\n              <div className=\"text-center pt-6\">\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"bg-primary-500 hover:bg-primary-600 text-white px-12 py-4 rounded-xl bold-16 transition-all transform hover:scale-105 shadow-lg\"\r\n                >\r\n                  Get Personalized Guidance\r\n                </button>\r\n                <p className=\"regular-14 text-gray-50 mt-4\">\r\n                  We'll analyze your information and provide helpful resources and recommendations.\r\n                </p>\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          {/* Reassurance Section */}\r\n          <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\r\n            <div className=\"text-center p-6 bg-white rounded-xl shadow-sm\">\r\n              <div className=\"w-12 h-12 bg-success-50 rounded-full mx-auto mb-4 flex items-center justify-center\">\r\n                <span className=\"text-2xl\">🔒</span>\r\n              </div>\r\n              <h3 className=\"bold-16 text-gray-90 mb-2\">Private & Secure</h3>\r\n              <p className=\"regular-14 text-gray-50\">Your information is kept confidential and secure</p>\r\n            </div>\r\n            <div className=\"text-center p-6 bg-white rounded-xl shadow-sm\">\r\n              <div className=\"w-12 h-12 bg-primary-100 rounded-full mx-auto mb-4 flex items-center justify-center\">\r\n                <span className=\"text-2xl\">💙</span>\r\n              </div>\r\n              <h3 className=\"bold-16 text-gray-90 mb-2\">Caring Support</h3>\r\n              <p className=\"regular-14 text-gray-50\">We're here to help with compassion and understanding</p>\r\n            </div>\r\n            <div className=\"text-center p-6 bg-white rounded-xl shadow-sm\">\r\n              <div className=\"w-12 h-12 bg-accent-50 rounded-full mx-auto mb-4 flex items-center justify-center\">\r\n                <span className=\"text-2xl\">⚡</span>\r\n              </div>\r\n              <h3 className=\"bold-16 text-gray-90 mb-2\">Quick Response</h3>\r\n              <p className=\"regular-14 text-gray-50\">Get immediate guidance and next steps</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default GetStartedPage\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,+RAAQ,EAAC;QACvC,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,6CAA6C;IAC/C;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAO,WAAU;0BAChB,cAAA,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC,qPAAI;wBAAC,MAAK;wBAAI,WAAU;kCACvB,cAAA,4TAAC;4BAAK,WAAU;sCAAuF;;;;;;;;;;;;;;;;;;;;;0BAM7G,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC;oBAAI,WAAU;;sCAEb,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;8CACb,cAAA,4TAAC,sNAAK;wCAAC,KAAI;wCAAY,KAAI;wCAAO,OAAO;wCAAI,QAAQ;wCAAI,WAAU;;;;;;;;;;;8CAErE,4TAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAGrD,4TAAC;oCAAE,WAAU;8CAAiD;;;;;;8CAI9D,4TAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;sCAOpD,4TAAC;4BAAI,WAAU;sCACb,cAAA,4TAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,4TAAC;;0DACC,4TAAC;gDAAM,SAAQ;gDAAO,WAAU;;oDAAkC;kEAEhE,4TAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,4TAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,4TAAC;;0DACC,4TAAC;gDAAM,SAAQ;gDAAW,WAAU;;oDAAkC;kEAEpE,4TAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAE7D,4TAAC;gDAAI,WAAU;;kEACb,4TAAC;wDAAE,WAAU;kEAA+B;;;;;;kEAC5C,4TAAC;wDAAI,WAAU;;0EACb,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;0EACN,4TAAC;0EAAK;;;;;;;;;;;;;;;;;;0DAGV,4TAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,aAAY;gDACZ,MAAM;gDACN,WAAU;gDACV,QAAQ;;;;;;0DAEV,4TAAC;gDAAE,WAAU;0DAAsC;;;;;;;;;;;;kDAMrD,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,4TAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;sCAQlD,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,4TAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,4TAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;8CAEzC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,4TAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,4TAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;8CAEzC,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,4TAAC;4CAAG,WAAU;sDAA4B;;;;;;sDAC1C,4TAAC;4CAAE,WAAU;sDAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrD;uCAEe", "debugId": null}}]}