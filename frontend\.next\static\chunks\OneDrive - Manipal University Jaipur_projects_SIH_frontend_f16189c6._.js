(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/constants/index.ts [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

// NAVIGATION
__turbopack_context__.s([
    "FEATURES",
    ()=>FEATURES,
    "FOOTER_CONTACT_INFO",
    ()=>FOOTER_CONTACT_INFO,
    "FOOTER_LINKS",
    ()=>FOOTER_LINKS,
    "NAV_LINKS",
    ()=>NAV_LINKS,
    "PEOPLE_URL",
    ()=>PEOPLE_URL,
    "SOCIALS",
    ()=>SOCIALS
]);
const NAV_LINKS = [
    {
        href: '/',
        key: 'home',
        label: 'Home'
    },
    {
        href: '/get-started',
        key: 'get_started',
        label: 'Get Started'
    },
    {
        href: '#testimonials',
        key: 'testimonials',
        label: 'Testimonials'
    },
    {
        href: '#statistics',
        key: 'statistics',
        label: 'Statistics'
    }
];
const PEOPLE_URL = [
    '/person-1.png',
    '/person-2.png',
    '/person-3.png',
    '/person-4.png'
];
const FEATURES = [
    {
        title: 'Real maps can be offline',
        icon: '/map.svg',
        variant: 'green',
        description: 'We provide a solution for you to be able to use our application when climbing, yes offline maps you can use at any time there is no signal at the location'
    },
    {
        title: 'Set an adventure schedule',
        icon: '/calendar.svg',
        variant: 'green',
        description: "Schedule an adventure with friends. On holidays, there are many interesting offers from Hilink. That way, there's no more discussion"
    },
    {
        title: 'Technology using augment reality',
        icon: '/tech.svg',
        variant: 'green',
        description: 'Technology uses augmented reality as a guide to your hiking trail in the forest to the top of the mountain. Already supported by the latest technology without an internet connection'
    },
    {
        title: 'Many new locations every month',
        icon: '/location.svg',
        variant: 'orange',
        description: 'Lots of new locations every month, because we have a worldwide community of climbers who share their best experiences with climbing'
    }
];
const FOOTER_LINKS = [
    {
        title: 'Learn More',
        links: [
            'About Hilink',
            'Press Releases',
            'Environment',
            'Jobs',
            'Privacy Policy',
            'Contact Us'
        ]
    },
    {
        title: 'Our Community',
        links: [
            'Climbing xixixi',
            'Hiking hilink',
            'Hilink kinthill'
        ]
    }
];
const FOOTER_CONTACT_INFO = {
    title: 'Contact Us',
    links: [
        {
            label: 'Admin Officer',
            value: '123-456-7890'
        },
        {
            label: 'Email Officer',
            value: '<EMAIL>'
        }
    ]
};
const SOCIALS = {
    title: 'Social',
    links: [
        '/facebook.svg',
        '/instagram.svg',
        '/twitter.svg',
        '/youtube.svg',
        '/wordpress.svg'
    ]
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/constants/index.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
'use client';
;
;
;
;
;
const Navbar = ()=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].nav, {
        className: "bg-white/90 backdrop-blur-md border-b-2 border-primary-200 flexBetween max-container padding-container relative z-30 py-5 shadow-sm",
        initial: {
            opacity: 0,
            y: -20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.6
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                href: "/",
                className: "hover:opacity-80 transition-opacity",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "hilink-logo.svg",
                    alt: "logo",
                    width: 74,
                    height: 29
                }, void 0, false, {
                    fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
                    lineNumber: 17,
                    columnNumber: 13
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
                lineNumber: 16,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                className: "hidden h-full gap-12 lg:flex",
                children: __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$constants$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NAV_LINKS"].map((link, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].li, {
                        initial: {
                            opacity: 0,
                            y: -10
                        },
                        animate: {
                            opacity: 1,
                            y: 0
                        },
                        transition: {
                            duration: 0.4,
                            delay: 0.6 + index * 0.1
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: link.href,
                            className: "regular-16 text-gray-50 flexCenter cursor-pointer pb-1.5 transition-all hover:font-bold",
                            children: link.label
                        }, void 0, false, {
                            fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
                            lineNumber: 28,
                            columnNumber: 19
                        }, ("TURBOPACK compile-time value", void 0))
                    }, link.key, false, {
                        fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
                        lineNumber: 22,
                        columnNumber: 17
                    }, ("TURBOPACK compile-time value", void 0)))
            }, void 0, false, {
                fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
                lineNumber: 20,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$OneDrive__$2d$__Manipal__University__Jaipur$2f$projects$2f$SIH$2f$frontend$2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                src: "menu.svg",
                alt: "menu",
                width: 32,
                height: 32,
                className: "inline-block cursor-pointer lg:hidden hover:opacity-70 transition-opacity"
            }, void 0, false, {
                fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
                lineNumber: 35,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/OneDrive - Manipal University Jaipur/projects/SIH/frontend/components/Navbar.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = Navbar;
const __TURBOPACK__default__export__ = Navbar;
var _c;
__turbopack_context__.k.register(_c, "Navbar");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=OneDrive%20-%20Manipal%20University%20Jaipur_projects_SIH_frontend_f16189c6._.js.map