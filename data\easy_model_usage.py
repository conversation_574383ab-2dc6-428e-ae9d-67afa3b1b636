#!/usr/bin/env python3
"""
Easy Usage Examples for Optimized Waterborne Disease Prediction Model
"""

from optimized_waterborne_model import WaterborneDiseasePredictionModel
import pandas as pd

def train_new_model():
    """Example: Train a new model from scratch"""
    print("🚀 Training New Model Example")
    print("=" * 40)
    
    # Initialize model
    model = WaterborneDiseasePredictionModel()
    
    # Train with default settings (uses existing data files)
    results = model.train(n_synthetic=50)
    
    print(f"\n✅ Training completed!")
    print(f"Final accuracy: {results['cv_accuracy_mean']:.1%}")
    
    return model

def retrain_with_new_data():
    """Example: Retrain model with new data files"""
    print("\n🔄 Retraining with New Data Example")
    print("=" * 40)
    
    # Initialize model
    model = WaterborneDiseasePredictionModel()
    
    # Train with custom data files (if you have new data)
    # results = model.train(
    #     disease_file='new_disease_data.csv',
    #     water_file='new_water_quality_data.csv',
    #     n_synthetic=100  # More synthetic data for better performance
    # )
    
    # For demo, use existing files
    results = model.train(n_synthetic=75)  # More synthetic samples
    
    print(f"\n✅ Retraining completed!")
    print(f"New accuracy: {results['cv_accuracy_mean']:.1%}")
    
    return model

def load_existing_model():
    """Example: Load a previously trained model"""
    print("\n📂 Loading Existing Model Example")
    print("=" * 40)
    
    # Initialize model
    model = WaterborneDiseasePredictionModel()
    
    try:
        # Load existing model
        model.load_model('optimized_waterborne_model')
        return model
    except FileNotFoundError:
        print("❌ No existing model found. Training new model...")
        return train_new_model()

def make_predictions(model):
    """Example: Make predictions with the model"""
    print("\n🔮 Making Predictions Example")
    print("=" * 40)
    
    # Single prediction
    print("1. Single Water Sample Prediction:")
    high_risk_sample = {
        'temperature': 28,
        'ph': 4.2,
        'dissolved_oxygen': 2.5,
        'bod': 20,
        'nitrate_n': 18,
        'fecal_coliform': 300,
        'total_coliform': 1200,
        'fecal_streptococci': 80,
        'water_quality_risk_score': 6
    }
    
    result = model.predict(high_risk_sample)
    print(f"   Risk Level: {result['risk_level']}")
    print(f"   Probability: {result['probability']:.1%}")
    print(f"   Prediction: {'⚠️ Outbreak Risk' if result['prediction'] == 1 else '✅ Safe'}")
    
    # Multiple predictions
    print("\n2. Multiple Water Samples Prediction:")
    multiple_samples = [
        {  # Clean water
            'temperature': 20, 'ph': 7.2, 'dissolved_oxygen': 8, 'bod': 2,
            'nitrate_n': 1, 'fecal_coliform': 0, 'total_coliform': 5,
            'fecal_streptococci': 0, 'water_quality_risk_score': 0
        },
        {  # Moderately contaminated
            'temperature': 24, 'ph': 6.8, 'dissolved_oxygen': 5, 'bod': 8,
            'nitrate_n': 12, 'fecal_coliform': 50, 'total_coliform': 200,
            'fecal_streptococci': 20, 'water_quality_risk_score': 3
        },
        {  # Highly contaminated
            'temperature': 30, 'ph': 9.2, 'dissolved_oxygen': 1, 'bod': 25,
            'nitrate_n': 20, 'fecal_coliform': 500, 'total_coliform': 2000,
            'fecal_streptococci': 100, 'water_quality_risk_score': 6
        }
    ]
    
    results = model.predict(multiple_samples)
    
    for i, (sample, pred, prob, risk) in enumerate(zip(
        multiple_samples, results['predictions'], results['probabilities'], results['risk_levels']
    )):
        print(f"   Sample {i+1}: {risk} Risk ({prob:.1%}) - {'⚠️ Outbreak' if pred == 1 else '✅ Safe'}")

def batch_prediction_from_csv(model):
    """Example: Batch prediction from CSV file"""
    print("\n📊 Batch Prediction from CSV Example")
    print("=" * 40)
    
    # Create sample CSV data
    sample_data = pd.DataFrame([
        {'temperature': 22, 'ph': 7.0, 'dissolved_oxygen': 7, 'bod': 3, 'nitrate_n': 2,
         'fecal_coliform': 0, 'total_coliform': 10, 'fecal_streptococci': 0, 'water_quality_risk_score': 0},
        {'temperature': 26, 'ph': 5.8, 'dissolved_oxygen': 4, 'bod': 12, 'nitrate_n': 15,
         'fecal_coliform': 100, 'total_coliform': 400, 'fecal_streptococci': 30, 'water_quality_risk_score': 4},
        {'temperature': 32, 'ph': 9.5, 'dissolved_oxygen': 1.5, 'bod': 30, 'nitrate_n': 25,
         'fecal_coliform': 800, 'total_coliform': 3000, 'fecal_streptococci': 150, 'water_quality_risk_score': 6}
    ])
    
    # Save to CSV
    sample_data.to_csv('sample_water_data.csv', index=False)
    print("   Created sample_water_data.csv")
    
    # Load and predict
    new_data = pd.read_csv('sample_water_data.csv')
    results = model.predict(new_data.to_dict('records'))
    
    # Add results to dataframe
    new_data['prediction'] = results['predictions']
    new_data['probability'] = results['probabilities']
    new_data['risk_level'] = results['risk_levels']
    
    print("   Prediction Results:")
    for i, row in new_data.iterrows():
        print(f"   Sample {i+1}: {row['risk_level']} Risk ({row['probability']:.1%})")
    
    # Save results
    new_data.to_csv('water_predictions.csv', index=False)
    print("   Results saved to water_predictions.csv")

def model_performance_summary(model):
    """Display model performance summary"""
    print("\n📈 Model Performance Summary")
    print("=" * 40)
    
    metadata = model.metadata
    print(f"Model: {metadata['model_name']}")
    print(f"Training Date: {metadata['training_date'][:10]}")
    print(f"Cross-validation Accuracy: {metadata['cv_accuracy_mean']:.1%} ± {metadata['cv_accuracy_std']:.1%}")
    print(f"AUC Score: {metadata['auc_score']:.3f}")
    print(f"Training Samples: {metadata['total_samples']} (Original: {metadata['original_samples']}, Synthetic: {metadata['synthetic_samples']})")
    print(f"Features: {len(metadata['feature_columns'])}")

def quick_start_guide():
    """Complete quick start guide"""
    print("🎯 Optimized Waterborne Disease Prediction Model")
    print("=" * 50)
    print("Quick Start Guide - Easy Model Usage")
    print("=" * 50)
    
    # Option 1: Try to load existing model, train if not found
    try:
        model = WaterborneDiseasePredictionModel()
        model.load_model('optimized_waterborne_model')
        print("✅ Loaded existing trained model")
    except FileNotFoundError:
        print("📝 No existing model found. Training new model...")
        model = train_new_model()
    
    # Show model performance
    model_performance_summary(model)
    
    # Make some predictions
    make_predictions(model)
    
    # Batch prediction example
    batch_prediction_from_csv(model)
    
    print("\n🎉 Quick start completed!")
    print("\n💡 Next Steps:")
    print("   1. Replace sample data with your actual water quality data")
    print("   2. Retrain model with: model.train(disease_file='your_disease_data.csv', water_file='your_water_data.csv')")
    print("   3. Use model.predict() for real-time predictions")
    print("   4. Save/load models with model.save_model() and model.load_model()")

if __name__ == "__main__":
    # Run the complete quick start guide
    quick_start_guide()
    
    print("\n" + "="*50)
    print("🔧 Advanced Usage Examples:")
    print("="*50)
    
    # Show how to retrain with more data
    print("\n💡 To retrain with new data:")
    print("   model = WaterborneDiseasePredictionModel()")
    print("   model.train(disease_file='new_disease_data.csv', water_file='new_water_data.csv', n_synthetic=100)")
    
    print("\n💡 To make API-ready predictions:")
    print("   result = model.predict({'temperature': 25, 'ph': 6.5, ...})")
    print("   return {'risk_level': result['risk_level'], 'probability': result['probability']}")
    
    print("\n💡 To update model with new samples:")
    print("   # Add new data to your CSV files and retrain")
    print("   model.train()  # Uses updated CSV files automatically")
