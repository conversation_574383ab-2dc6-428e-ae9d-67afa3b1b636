#!/usr/bin/env python3
"""
Test the trained waterborne disease prediction model
"""

import pandas as pd
import numpy as np
import joblib
import json
from datetime import datetime

def load_model():
    """Load the trained model and scaler"""
    try:
        model = joblib.load('waterborne_disease_model.pkl')
        scaler = joblib.load('water_quality_scaler.pkl')
        
        with open('model_metadata.json', 'r') as f:
            metadata = json.load(f)
        
        print("✅ Model loaded successfully!")
        print(f"Model: {metadata['model_name']}")
        print(f"Training date: {metadata['training_date']}")
        print(f"CV Score: {metadata['cv_score']:.3f}")
        
        return model, scaler, metadata
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return None, None, None

def predict_waterborne_disease_risk(model, scaler, metadata, temperature, ph, dissolved_oxygen, 
                                   bod, nitrate_n, fecal_coliform, total_coliform, fecal_streptococci):
    """
    Predict waterborne disease outbreak risk based on water quality parameters.
    """
    
    # Create water quality risk indicators
    ph_risk = 1 if (ph < 6.5 or ph > 8.5) else 0
    do_risk = 1 if dissolved_oxygen < 5 else 0
    bod_risk = 1 if bod > 5 else 0
    nitrate_risk = 1 if nitrate_n > 10 else 0
    fecal_coliform_risk = 1 if fecal_coliform > 0 else 0
    total_coliform_risk = 1 if total_coliform > 0 else 0
    
    water_quality_risk_score = (ph_risk + do_risk + bod_risk + nitrate_risk + 
                               fecal_coliform_risk + total_coliform_risk)
    
    # Prepare input features
    input_features = np.array([[temperature, ph, dissolved_oxygen, bod, nitrate_n,
                               fecal_coliform, total_coliform, fecal_streptococci,
                               water_quality_risk_score]])
    
    # Make prediction
    if metadata['model_name'] in ['Logistic Regression', 'Naive Bayes']:
        input_scaled = scaler.transform(input_features)
        prediction = model.predict(input_scaled)[0]
        probability = model.predict_proba(input_scaled)[0][1] if hasattr(model, 'predict_proba') else 0.5
    else:
        prediction = model.predict(input_features)[0]
        probability = model.predict_proba(input_features)[0][1] if hasattr(model, 'predict_proba') else 0.5
    
    # Determine risk level
    if probability < 0.3:
        risk_level = "Low"
        risk_color = "green"
    elif probability < 0.7:
        risk_level = "Medium"
        risk_color = "orange"
    else:
        risk_level = "High"
        risk_color = "red"
    
    # Generate recommendations
    recommendations = []
    
    if ph_risk:
        recommendations.append("pH levels are outside safe range (6.5-8.5). Consider water treatment.")
    if do_risk:
        recommendations.append("Low dissolved oxygen detected. Check for organic pollution.")
    if bod_risk:
        recommendations.append("High BOD indicates organic contamination. Improve wastewater treatment.")
    if nitrate_risk:
        recommendations.append("High nitrate levels detected. Check for agricultural runoff.")
    if fecal_coliform_risk:
        recommendations.append("Fecal coliform detected. Implement disinfection measures immediately.")
    if total_coliform_risk:
        recommendations.append("Total coliform detected. Monitor water source and treatment systems.")
    
    if not recommendations:
        recommendations.append("Water quality parameters are within acceptable ranges. Continue regular monitoring.")
    
    if risk_level == "High":
        recommendations.append("HIGH RISK: Implement immediate public health measures and water advisories.")
    elif risk_level == "Medium":
        recommendations.append("MEDIUM RISK: Increase monitoring frequency and prepare preventive measures.")
    
    return {
        'prediction': int(prediction),
        'probability': float(probability),
        'risk_level': risk_level,
        'risk_color': risk_color,
        'water_quality_risk_score': int(water_quality_risk_score),
        'recommendations': recommendations,
        'model_used': metadata['model_name'],
        'prediction_timestamp': datetime.now().isoformat()
    }

def main():
    """Test the model with different scenarios"""
    
    print("🧪 Testing Waterborne Disease Prediction Model")
    print("=" * 50)
    
    # Load the model
    model, scaler, metadata = load_model()
    if model is None:
        return
    
    # Test scenarios
    test_cases = [
        {
            'name': 'High Risk Scenario',
            'params': {
                'temperature': 25,
                'ph': 4.5,
                'dissolved_oxygen': 3,
                'bod': 15,
                'nitrate_n': 15,
                'fecal_coliform': 200,
                'total_coliform': 1000,
                'fecal_streptococci': 50
            }
        },
        {
            'name': 'Medium Risk Scenario',
            'params': {
                'temperature': 22,
                'ph': 6.0,
                'dissolved_oxygen': 6,
                'bod': 8,
                'nitrate_n': 5,
                'fecal_coliform': 50,
                'total_coliform': 100,
                'fecal_streptococci': 20
            }
        },
        {
            'name': 'Low Risk Scenario',
            'params': {
                'temperature': 20,
                'ph': 7.2,
                'dissolved_oxygen': 8,
                'bod': 2,
                'nitrate_n': 3,
                'fecal_coliform': 0,
                'total_coliform': 0,
                'fecal_streptococci': 0
            }
        },
        {
            'name': 'Real Assam Sample (from dataset)',
            'params': {
                'temperature': 18.5,
                'ph': 7.8,
                'dissolved_oxygen': 19.2,
                'bod': 8.72,
                'nitrate_n': 7.0,
                'fecal_coliform': 46.0,
                'total_coliform': 674.0,
                'fecal_streptococci': 25.0
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 30)
        
        # Make prediction
        result = predict_waterborne_disease_risk(model, scaler, metadata, **test_case['params'])
        
        # Display results
        print(f"🎯 Prediction: {'Outbreak Risk' if result['prediction'] == 1 else 'No Outbreak Risk'}")
        print(f"📊 Risk Level: {result['risk_level']} (Probability: {result['probability']:.3f})")
        print(f"💧 Water Quality Risk Score: {result['water_quality_risk_score']}/6")
        print(f"🔬 Model Used: {result['model_used']}")
        
        print(f"\n📋 Recommendations ({len(result['recommendations'])} items):")
        for j, rec in enumerate(result['recommendations'], 1):
            print(f"   {j}. {rec}")
        
        print(f"\n⏰ Prediction Time: {result['prediction_timestamp']}")
    
    print("\n" + "=" * 50)
    print("✅ Model testing completed successfully!")
    print("\n💡 The model is ready for integration with your frontend application.")
    print("   You can use the predict_waterborne_disease_risk() function in your API backend.")

if __name__ == "__main__":
    main()
