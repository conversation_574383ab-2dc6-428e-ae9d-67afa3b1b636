{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/constants/index.ts"], "sourcesContent": ["// NAVIGATION\r\nexport const NAV_LINKS = [\r\n  { href: '/', key: 'home', label: 'Home' },\r\n  { href: '/get-started', key: 'get_started', label: 'Get Started' },\r\n  { href: '#testimonials', key: 'testimonials', label: 'Testimonials' },\r\n  { href: '#statistics', key: 'statistics', label: 'Statistics' },\r\n];\r\n\r\n// CAMP SECTION\r\nexport const PEOPLE_URL = [\r\n  '/person-1.png',\r\n  '/person-2.png',\r\n  '/person-3.png',\r\n  '/person-4.png',\r\n];\r\n\r\n// FEATURES SECTION\r\nexport const FEATURES = [\r\n  {\r\n    title: 'Real maps can be offline',\r\n    icon: '/map.svg',\r\n    variant: 'green',\r\n    description:\r\n      'We provide a solution for you to be able to use our application when climbing, yes offline maps you can use at any time there is no signal at the location',\r\n  },\r\n  {\r\n    title: 'Set an adventure schedule',\r\n    icon: '/calendar.svg',\r\n    variant: 'green',\r\n    description:\r\n      \"Schedule an adventure with friends. On holidays, there are many interesting offers from Hilink. That way, there's no more discussion\",\r\n  },\r\n  {\r\n    title: 'Technology using augment reality',\r\n    icon: '/tech.svg',\r\n    variant: 'green',\r\n    description:\r\n      'Technology uses augmented reality as a guide to your hiking trail in the forest to the top of the mountain. Already supported by the latest technology without an internet connection',\r\n  },\r\n  {\r\n    title: 'Many new locations every month',\r\n    icon: '/location.svg',\r\n    variant: 'orange',\r\n    description:\r\n      'Lots of new locations every month, because we have a worldwide community of climbers who share their best experiences with climbing',\r\n  },\r\n];\r\n\r\n// FOOTER SECTION\r\nexport const FOOTER_LINKS = [\r\n  {\r\n    title: 'Learn More',\r\n    links: [\r\n      'About Hilink',\r\n      'Press Releases',\r\n      'Environment',\r\n      'Jobs',\r\n      'Privacy Policy',\r\n      'Contact Us',\r\n    ],\r\n  },\r\n  {\r\n    title: 'Our Community',\r\n    links: ['Climbing xixixi', 'Hiking hilink', 'Hilink kinthill'],\r\n  },\r\n];\r\n\r\nexport const FOOTER_CONTACT_INFO = {\r\n  title: 'Contact Us',\r\n  links: [\r\n    { label: 'Admin Officer', value: '123-456-7890' },\r\n    { label: 'Email Officer', value: '<EMAIL>' },\r\n  ],\r\n};\r\n\r\nexport const SOCIALS = {\r\n  title: 'Social',\r\n  links: [\r\n    '/facebook.svg',\r\n    '/instagram.svg',\r\n    '/twitter.svg',\r\n    '/youtube.svg',\r\n    '/wordpress.svg',\r\n  ],\r\n};"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;;;;;;;AACN,MAAM,YAAY;IACvB;QAAE,MAAM;QAAK,KAAK;QAAQ,OAAO;IAAO;IACxC;QAAE,MAAM;QAAgB,KAAK;QAAe,OAAO;IAAc;IACjE;QAAE,MAAM;QAAiB,KAAK;QAAgB,OAAO;IAAe;IACpE;QAAE,MAAM;QAAe,KAAK;QAAc,OAAO;IAAa;CAC/D;AAGM,MAAM,aAAa;IACxB;IACA;IACA;IACA;CACD;AAGM,MAAM,WAAW;IACtB;QACE,OAAO;QACP,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,OAAO;QACP,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,OAAO;QACP,MAAM;QACN,SAAS;QACT,aACE;IACJ;IACA;QACE,OAAO;QACP,MAAM;QACN,SAAS;QACT,aACE;IACJ;CACD;AAGM,MAAM,eAAe;IAC1B;QACE,OAAO;QACP,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YAAC;YAAmB;YAAiB;SAAkB;IAChE;CACD;AAEM,MAAM,sBAAsB;IACjC,OAAO;IACP,OAAO;QACL;YAAE,OAAO;YAAiB,OAAO;QAAe;QAChD;YAAE,OAAO;YAAiB,OAAO;QAAsB;KACxD;AACH;AAEO,MAAM,UAAU;IACrB,OAAO;IACP,OAAO;QACL;QACA;QACA;QACA;QACA;KACD;AACH", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive%20-%20Manipal%20University%20Jaipur/projects/SIH/frontend/components/Navbar.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { NAV_LINKS } from \"@/constants\"\r\nimport Image from \"next/image\"\r\nimport Link from \"next/link\"\r\nimport { motion } from 'framer-motion'\r\n\r\nconst Navbar = () => {\r\n  return (\r\n    <motion.nav\r\n      className=\"bg-white/90 backdrop-blur-md border-b-2 border-primary-200 flexBetween max-container padding-container relative z-30 py-5 shadow-sm\"\r\n      initial={{ opacity: 0, y: -20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.6 }}\r\n    >\r\n        <Link href=\"/\" className=\"hover:opacity-80 transition-opacity\">\r\n            <Image src=\"hilink-logo.svg\" alt=\"logo\" width={74} height={29}></Image>\r\n        </Link>\r\n\r\n        <ul className=\"hidden h-full gap-12 lg:flex\">\r\n            {NAV_LINKS.map((link, index)=> (\r\n                <motion.li\r\n                  key={link.key}\r\n                  initial={{ opacity: 0, y: -10 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}\r\n                >\r\n                  <Link href={link.href} className=\"regular-16 text-gray-50 flexCenter cursor-pointer pb-1.5 transition-all hover:font-bold\">\r\n                      {link.label}\r\n                  </Link>\r\n                </motion.li>\r\n            ))}\r\n        </ul>\r\n\r\n        <Image src=\"menu.svg\" alt=\"menu\" width={32} height={32} className=\"inline-block cursor-pointer lg:hidden hover:opacity-70 transition-opacity\">\r\n\r\n        </Image>\r\n\r\n    </motion.nav>\r\n  )\r\n}\r\n\r\nexport default Navbar\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,qBACE,4TAAC,kRAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAE1B,4TAAC,qPAAI;gBAAC,MAAK;gBAAI,WAAU;0BACrB,cAAA,4TAAC,sNAAK;oBAAC,KAAI;oBAAkB,KAAI;oBAAO,OAAO;oBAAI,QAAQ;;;;;;;;;;;0BAG/D,4TAAC;gBAAG,WAAU;0BACT,6MAAS,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClB,4TAAC,kRAAM,CAAC,EAAE;wBAER,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,MAAM,QAAQ;wBAAI;kCAEtD,cAAA,4TAAC,qPAAI;4BAAC,MAAM,KAAK,IAAI;4BAAE,WAAU;sCAC5B,KAAK,KAAK;;;;;;uBANV,KAAK,GAAG;;;;;;;;;;0BAYvB,4TAAC,sNAAK;gBAAC,KAAI;gBAAW,KAAI;gBAAO,OAAO;gBAAI,QAAQ;gBAAI,WAAU;;;;;;;;;;;;AAM1E;uCAEe", "debugId": null}}]}